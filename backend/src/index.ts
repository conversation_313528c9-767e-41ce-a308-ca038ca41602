import express from 'express'
import cors from 'cors'
import dotenv from 'dotenv'
import aiRoutes from './routes/ai.js'
import videoRoutes from './routes/video.js'

// Load environment variables
dotenv.config() // Load from current directory first
dotenv.config({ path: '../.env' }) // Fallback to parent directory

const app = express()
const PORT = process.env.PORT || 3001

// Middleware
app.use(cors({
  origin: process.env.REACT_APP_API_URL || 'http://localhost:5173',
  credentials: true
}))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true }))

// Routes
app.use('/api/ai', aiRoutes)
app.use('/api/video', videoRoutes)

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  })
})

// Root endpoint
app.get('/', (req, res) => {
  res.json({ 
    message: 'CodeTutor AI Backend API',
    version: '1.0.0',
    endpoints: {
      health: '/api/health',
      ai: '/api/ai',
      video: '/api/video'
    }
  })
})

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Error:', err)
  res.status(500).json({ 
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  })
})

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' })
})

app.listen(PORT, () => {
  console.log(`🚀 CodeTutor AI Backend running on port ${PORT}`)
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`)
  console.log(`🤖 AI endpoint: http://localhost:${PORT}/api/ai`)
  console.log(`🎥 Video endpoint: http://localhost:${PORT}/api/video`)
})
