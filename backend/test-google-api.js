#!/usr/bin/env node

/**
 * Test script to verify Google AI API key is working correctly
 * Run this script to check if your Google AI API key is properly configured
 */

import dotenv from 'dotenv';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

dotenv.config({ path: join(__dirname, '../.env') });

async function testGoogleAPI() {
  console.log('🔍 Testing Google AI API Key...\n');

  // Check if API key is set
  const apiKey = 'AIzaSyCeVI68NmKdp2RchI_0V0olAa1D8yM-xrU';



  if (!apiKey) {
    console.log('❌ GOOGLE_AI_API_KEY environment variable is not set');
    console.log('Please add your Google AI API key to the .env file');
    process.exit(1);
  }

  // Check if it's still the placeholder key
  if (apiKey === 'your-google-ai-api-key') {
    console.log('❌ Google AI API key appears to be a placeholder');
    console.log('Please replace the placeholder with your actual Google AI API key');
    console.log('Get your API key from: https://makersuite.google.com/app/apikey');
    process.exit(1);
  }

  console.log('✅ API key found in environment variables');
  console.log(`🔑 API key format: ${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length - 4)}`);

  // Initialize Google AI
  try {
    const genAI = new GoogleGenerativeAI(apiKey);
    console.log('✅ GoogleGenerativeAI client initialized successfully');

    // Test with a simple prompt
    console.log('\n🧪 Testing API with a simple prompt...');
    
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    
    const prompt = "Say 'Hello, CodeTutor AI!' and confirm that the API is working.";
    
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    console.log('✅ API call successful!');
    console.log('📝 Response from Google AI:');
    console.log('─'.repeat(50));
    console.log(text);
    console.log('─'.repeat(50));

    // Test with a coding-related prompt
    console.log('\n🧪 Testing with a coding tutorial prompt...');
    
    const codingPrompt = `
    Create a brief tutorial step for: "How to create a React component"
    
    Respond in JSON format:
    {
      "title": "Step title",
      "description": "Brief description",
      "codeExample": "Simple code example"
    }
    `;

    const codingModel = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    const codingResult = await codingModel.generateContent(codingPrompt);
    const codingResponse = await codingResult.response;
    const codingText = codingResponse.text();

    console.log('✅ Coding tutorial prompt successful!');
    console.log('📝 Response:');
    console.log('─'.repeat(50));
    console.log(codingText);
    console.log('─'.repeat(50));

    console.log('\n🎉 Google AI API is working correctly!');
    console.log('✅ Your API key is valid and functional');
    console.log('✅ The AI can generate coding tutorials');
    console.log('✅ Ready for use in CodeTutor AI');

  } catch (error) {
    console.log('\n❌ API test failed!');
    console.error('Error details:', error.message);
    
    if (error.message.includes('API_KEY_INVALID')) {
      console.log('\n💡 Troubleshooting:');
      console.log('- Check that your API key is correct');
      console.log('- Ensure the API key has the necessary permissions');
      console.log('- Verify the API key is enabled for the Generative AI API');
    } else if (error.message.includes('PERMISSION_DENIED')) {
      console.log('\n💡 Troubleshooting:');
      console.log('- Enable the Generative AI API in Google Cloud Console');
      console.log('- Check that your API key has the correct permissions');
    } else if (error.message.includes('QUOTA_EXCEEDED')) {
      console.log('\n💡 Troubleshooting:');
      console.log('- You may have exceeded your API quota');
      console.log('- Check your usage in Google Cloud Console');
    } else {
      console.log('\n💡 Troubleshooting:');
      console.log('- Check your internet connection');
      console.log('- Verify the API key is correctly formatted');
      console.log('- Try generating a new API key');
    }
    
    process.exit(1);
  }
}

// Run the test
testGoogleAPI().catch(console.error);

export { testGoogleAPI };
