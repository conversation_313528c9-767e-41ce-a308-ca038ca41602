import React from 'react';
import {
  AbsoluteFill,
  interpolate,
  spring,
  useCurrentFrame,
  useVideoConfig,
} from 'remotion';

interface TutorialStep {
  stepNumber: number;
  title: string;
  description: string;
  codeExample?: string;
  uiActions: string[];
  expectedResult: string;
}

interface TutorialVideoProps {
  title: string;
  steps: TutorialStep[];
}

export const TutorialVideo: React.FC<TutorialVideoProps> = ({ title, steps }) => {
  const frame = useCurrentFrame();
  const { fps, durationInFrames } = useVideoConfig();

  // Calculate timing for each step
  const stepDuration = Math.floor(durationInFrames / steps.length);
  const currentStepIndex = Math.floor(frame / stepDuration);
  const currentStep = steps[currentStepIndex] || steps[0];

  // Animation values
  const titleOpacity = interpolate(frame, [0, 30], [0, 1], {
    extrapolateRight: 'clamp',
  });

  const stepProgress = spring({
    frame: frame % stepDuration,
    fps,
    config: {
      damping: 200,
      stiffness: 100,
    },
  });

  return (
    <AbsoluteFill style={{ backgroundColor: '#f8fafc' }}>
      {/* Header */}
      <div
        style={{
          position: 'absolute',
          top: 60,
          left: 60,
          right: 60,
          opacity: titleOpacity,
        }}
      >
        <h1
          style={{
            fontSize: 48,
            fontWeight: 'bold',
            color: '#1e293b',
            margin: 0,
            textAlign: 'center',
          }}
        >
          {title}
        </h1>
      </div>

      {/* Step Content */}
      <div
        style={{
          position: 'absolute',
          top: 200,
          left: 60,
          right: 60,
          bottom: 60,
          transform: `translateY(${(1 - stepProgress) * 20}px)`,
          opacity: stepProgress,
        }}
      >
        {/* Step Number and Title */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            marginBottom: 40,
          }}
        >
          <div
            style={{
              width: 60,
              height: 60,
              borderRadius: '50%',
              backgroundColor: '#3b82f6',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: 24,
              fontWeight: 'bold',
              marginRight: 20,
            }}
          >
            {currentStep.stepNumber}
          </div>
          <h2
            style={{
              fontSize: 36,
              fontWeight: 'bold',
              color: '#1e293b',
              margin: 0,
            }}
          >
            {currentStep.title}
          </h2>
        </div>

        {/* Description */}
        <div
          style={{
            backgroundColor: 'white',
            padding: 30,
            borderRadius: 12,
            marginBottom: 30,
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          }}
        >
          <p
            style={{
              fontSize: 24,
              color: '#475569',
              margin: 0,
              lineHeight: 1.6,
            }}
          >
            {currentStep.description}
          </p>
        </div>

        {/* Code Example */}
        {currentStep.codeExample && (
          <div
            style={{
              backgroundColor: '#1e293b',
              padding: 30,
              borderRadius: 12,
              marginBottom: 30,
              fontFamily: 'Monaco, Consolas, monospace',
            }}
          >
            <pre
              style={{
                color: '#e2e8f0',
                fontSize: 18,
                margin: 0,
                whiteSpace: 'pre-wrap',
              }}
            >
              {currentStep.codeExample}
            </pre>
          </div>
        )}

        {/* UI Actions */}
        <div
          style={{
            backgroundColor: '#f1f5f9',
            padding: 30,
            borderRadius: 12,
            marginBottom: 30,
          }}
        >
          <h3
            style={{
              fontSize: 20,
              fontWeight: 'bold',
              color: '#1e293b',
              marginTop: 0,
              marginBottom: 15,
            }}
          >
            Actions to take:
          </h3>
          <ul style={{ margin: 0, paddingLeft: 20 }}>
            {currentStep.uiActions.map((action, index) => (
              <li
                key={index}
                style={{
                  fontSize: 18,
                  color: '#475569',
                  marginBottom: 8,
                }}
              >
                {action}
              </li>
            ))}
          </ul>
        </div>

        {/* Expected Result */}
        <div
          style={{
            backgroundColor: '#dcfce7',
            padding: 30,
            borderRadius: 12,
            border: '2px solid #16a34a',
          }}
        >
          <h3
            style={{
              fontSize: 20,
              fontWeight: 'bold',
              color: '#15803d',
              marginTop: 0,
              marginBottom: 15,
            }}
          >
            Expected Result:
          </h3>
          <p
            style={{
              fontSize: 18,
              color: '#166534',
              margin: 0,
            }}
          >
            {currentStep.expectedResult}
          </p>
        </div>
      </div>

      {/* Progress Indicator */}
      <div
        style={{
          position: 'absolute',
          bottom: 30,
          left: 60,
          right: 60,
          height: 4,
          backgroundColor: '#e2e8f0',
          borderRadius: 2,
        }}
      >
        <div
          style={{
            height: '100%',
            backgroundColor: '#3b82f6',
            borderRadius: 2,
            width: `${((currentStepIndex + stepProgress) / steps.length) * 100}%`,
            transition: 'width 0.3s ease',
          }}
        />
      </div>
    </AbsoluteFill>
  );
};
