import { Composition } from 'remotion';
import { TutorialVideo } from './TutorialVideo';

export const RemotionRoot: React.FC = () => {
  return (
    <>
      <Composition
        id="TutorialVideo"
        component={TutorialVideo}
        durationInFrames={300} // 10 seconds at 30fps
        fps={30}
        width={1920}
        height={1080}
        defaultProps={{
          title: "Sample Tutorial",
          steps: [
            {
              stepNumber: 1,
              title: "Step 1: Getting Started",
              description: "This is the first step of your tutorial",
              codeExample: "console.log('Hello, World!');",
              uiActions: ["Open your code editor", "Create a new file"],
              expectedResult: "You should see a new file in your editor"
            }
          ]
        }}
      />
    </>
  );
};
