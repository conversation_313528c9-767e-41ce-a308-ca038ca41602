import type { JumpMark } from '../../containers/iso-base-media/mdat/calculate-jump-marks';
import type { SamplePosition } from '../../get-sample-positions';
import type { MediaParserAudioTrack, MediaParserOtherTrack, MediaParserVideoTrack } from '../../get-tracks';
import type { ParserState } from '../parser-state';
export type FlatSample = {
    track: MediaParserVideoTrack | MediaParserAudioTrack | MediaParserOtherTrack;
    samplePosition: SamplePosition;
};
export type MinimalFlatSampleForTesting = {
    track: {
        trackId: number;
        originalTimescale: number;
        type: 'audio' | 'video' | 'other';
    };
    samplePosition: {
        decodingTimestamp: number;
        offset: number;
    };
};
export declare const calculateFlatSamples: ({ state, mediaSectionStart, }: {
    state: ParserState;
    mediaSectionStart: number;
}) => {
    track: import("../../get-tracks").MediaParserTrack;
    samplePosition: SamplePosition;
}[][];
export declare const cachedSamplePositionsState: () => {
    getSamples: (mdatStart: number) => FlatSample[];
    setSamples: (mdatStart: number, samples: FlatSample[]) => void;
    setJumpMarks: (mdatStart: number, marks: JumpMark[]) => void;
    getJumpMarks: (mdatStart: number) => JumpMark[];
};
