import type { MediaParserCodecData } from './codec-data';
import type { MediaParserMatrixCoefficients, MediaParserPrimaries, MediaParserTransferCharacteristics } from './containers/avc/color';
import type { MoovBox } from './containers/iso-base-media/moov/moov';
import type { TrakBox } from './containers/iso-base-media/trak/trak';
import type { M3uPlaylistContext } from './options';
import type { IsoBaseMediaState } from './state/iso-base-media/iso-state';
import type { ParserState } from './state/parser-state';
import type { StructureState } from './state/structure';
export type MediaParserSampleAspectRatio = {
    numerator: number;
    denominator: number;
};
export type MediaParserAdvancedColor = {
    transfer: MediaParserTransferCharacteristics | null;
    matrix: MediaParserMatrixCoefficients | null;
    primaries: MediaParserPrimaries | null;
    fullRange: boolean | null;
};
export type MediaParserVideoCodec = 'vp8' | 'vp9' | 'h264' | 'av1' | 'h265' | 'prores';
export type MediaParserAudioCodec = 'opus' | 'aac' | 'mp3' | 'ac3' | 'vorbis' | 'pcm-u8' | 'pcm-s16' | 'pcm-s24' | 'pcm-s32' | 'pcm-f32' | 'flac' | 'aiff';
export type MediaParserVideoTrack = {
    codec: string;
    description: Uint8Array | undefined;
    colorSpace: VideoColorSpaceInit;
    codedWidth: number;
    codedHeight: number;
    displayAspectWidth: number;
    displayAspectHeight: number;
    type: 'video';
    trackId: number;
    codecEnum: MediaParserVideoCodec;
    codecData: MediaParserCodecData | null;
    sampleAspectRatio: MediaParserSampleAspectRatio;
    width: number;
    height: number;
    rotation: number;
    fps: number | null;
    timescale: 1000000;
    originalTimescale: number;
    advancedColor: MediaParserAdvancedColor;
    m3uStreamFormat: 'ts' | 'mp4' | null;
    startInSeconds: number;
};
export type MediaParserAudioTrack = {
    codec: string;
    sampleRate: number;
    description: Uint8Array | undefined;
    numberOfChannels: number;
    type: 'audio';
    trackId: number;
    codecEnum: MediaParserAudioCodec;
    timescale: 1000000;
    originalTimescale: number;
    codecData: MediaParserCodecData | null;
    startInSeconds: number;
};
export type MediaParserOtherTrack = {
    type: 'other';
    trackId: number;
    timescale: 1000000;
    originalTimescale: number;
    trakBox: TrakBox | null;
    startInSeconds: number;
};
export type MediaParserTrack = MediaParserVideoTrack | MediaParserAudioTrack | MediaParserOtherTrack;
export declare const getNumberOfTracks: (moovBox: MoovBox) => number;
export declare const isoBaseMediaHasTracks: (state: ParserState, mayUsePrecomputed: boolean) => boolean;
export declare const getHasTracks: (state: ParserState, mayUsePrecomputed: boolean) => boolean;
export declare const getTracksFromMoovBox: (moovBox: MoovBox) => MediaParserTrack[];
export declare const getTracksFromIsoBaseMedia: ({ mayUsePrecomputed, structure, isoState, m3uPlaylistContext, }: {
    structure: StructureState;
    isoState: IsoBaseMediaState;
    m3uPlaylistContext: M3uPlaylistContext | null;
    mayUsePrecomputed: boolean;
}) => MediaParserTrack[];
export declare const defaultGetTracks: (parserState: ParserState) => MediaParserTrack[];
export declare const defaultHasallTracks: (parserState: ParserState) => boolean;
export declare const getTracks: (state: ParserState, mayUsePrecomputed: boolean) => MediaParserTrack[];
