import type { MediaParserLogLevel } from '../../log';
import type { TransportStreamStructure } from '../../parse-result';
import type { AvcState } from '../../state/avc/avc-state';
import type { CallbacksState } from '../../state/sample-callbacks';
import type { TransportStreamState } from '../../state/transport-stream/transport-stream';
import type { MediaParserOnAudioTrack, MediaParserOnVideoTrack } from '../../webcodec-sample-types';
import type { TransportStreamPacketBuffer } from './process-stream-buffers';
export declare const canProcessVideo: ({ streamBuffer, }: {
    streamBuffer: TransportStreamPacketBuffer;
}) => boolean;
export declare const processVideo: ({ programId, structure, streamBuffer, sampleCallbacks, logLevel, onAudioTrack, onVideoTrack, transportStream, makeSamplesStartAtZero, avcState, }: {
    programId: number;
    structure: TransportStreamStructure;
    streamBuffer: TransportStreamPacketBuffer;
    sampleCallbacks: CallbacksState;
    logLevel: MediaParserLogLevel;
    onAudioTrack: MediaParserOnAudioTrack | null;
    onVideoTrack: MediaParserOnVideoTrack | null;
    transportStream: TransportStreamState;
    makeSamplesStartAtZero: boolean;
    avcState: AvcState;
}) => Promise<Uint8Array>;
