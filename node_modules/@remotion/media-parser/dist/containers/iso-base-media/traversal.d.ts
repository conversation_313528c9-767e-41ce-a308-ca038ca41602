import type { AnySegment, IsoBaseMediaStructure } from '../../parse-result';
import type { IsoBaseMediaState } from '../../state/iso-base-media/iso-state';
import type { MoofBox } from '../../state/iso-base-media/precomputed-moof';
import type { StructureState } from '../../state/structure';
import type { IsoBaseMediaBox, RegularBox } from './base-media-box';
import type { ElstBox } from './elst';
import type { FtypBox } from './ftyp';
import type { MdhdBox } from './mdhd';
import type { TfraBox } from './mfra/tfra';
import type { MoovBox } from './moov/moov';
import type { MvhdBox } from './moov/mvhd';
import type { TrexBox } from './moov/trex';
import type { CttsBox } from './stsd/ctts';
import type { StcoBox } from './stsd/stco';
import type { StscBox } from './stsd/stsc';
import type { StsdBox } from './stsd/stsd';
import type { StssBox } from './stsd/stss';
import type { StszBox } from './stsd/stsz';
import type { SttsBox } from './stsd/stts';
import type { TfdtBox } from './tfdt';
import type { TfhdBox } from './tfhd';
import type { TkhdBox } from './tkhd';
import type { TrakBox } from './trak/trak';
import type { TrunBox } from './trun';
export declare const getFtypBox: (segments: AnySegment[]) => FtypBox | null;
export declare const getMoovFromFromIsoStructure: (structure: IsoBaseMediaStructure) => MoovBox | null;
export declare const getMoovBoxFromState: ({ structureState, isoState, mp4HeaderSegment, mayUsePrecomputed, }: {
    structureState: StructureState;
    isoState: IsoBaseMediaState;
    mp4HeaderSegment: IsoBaseMediaStructure | null;
    mayUsePrecomputed: boolean;
}) => MoovBox | null;
export declare const getMoofBoxes: (main: AnySegment[]) => MoofBox[];
export declare const getMvhdBox: (moovBox: MoovBox) => MvhdBox | null;
export declare const getTraks: (moovBox: MoovBox) => TrakBox[];
export declare const getTkhdBox: (trakBox: TrakBox) => TkhdBox | null;
export declare const getMdiaBox: (trakBox: TrakBox) => RegularBox | null;
export declare const getMdhdBox: (trakBox: TrakBox) => MdhdBox | null;
export declare const getStblBox: (trakBox: TrakBox) => RegularBox | null;
export declare const getStsdBox: (trakBox: TrakBox) => StsdBox | null;
export declare const getVideoDescriptors: (trakBox: TrakBox) => Uint8Array | null;
export declare const getStcoBox: (trakBox: TrakBox) => StcoBox | null;
export declare const getSttsBox: (trakBox: TrakBox) => SttsBox | null;
export declare const getCttsBox: (trakBox: TrakBox) => CttsBox | null;
export declare const getStszBox: (trakBox: TrakBox) => StszBox | null;
export declare const getStscBox: (trakBox: TrakBox) => StscBox | null;
export declare const getStssBox: (trakBox: TrakBox) => StssBox | null;
export declare const getTfdtBox: (segment: IsoBaseMediaBox) => TfdtBox | null;
export declare const getTfhdBox: (segment: IsoBaseMediaBox) => TfhdBox | null;
export declare const getTrunBoxes: (segment: IsoBaseMediaBox) => TrunBox[];
export declare const getMvexBox: (moovAtom: MoovBox) => RegularBox | null;
export declare const getTrexBoxes: (moovAtom: MoovBox) => TrexBox[];
export declare const getTfraBoxesFromMfraBoxChildren: (mfraBoxChildren: IsoBaseMediaBox[]) => TfraBox[];
export declare const getTfraBoxes: (structure: IsoBaseMediaBox[]) => TfraBox[];
export declare const getTrakBoxByTrackId: (moovBox: MoovBox, trackId: number) => TrakBox | null;
export declare const getElstBox: (trakBox: TrakBox) => ElstBox | null;
