import type { MatroskaSegment } from './segments';
import type { AudioSegment, ClusterSegment, CodecIdSegment, ColourSegment, DisplayHeightSegment, DisplayWidthSegment, HeightSegment, MainSegment, MatrixCoefficientsSegment, PrimariesSegment, RangeSegment, TimestampScaleSegment, TrackEntry, TrackTypeSegment, TransferCharacteristicsSegment, VideoSegment, WidthSegment } from './segments/all-segments';
export declare const getMainSegment: (segments: MatroskaSegment[]) => MainSegment | null;
export declare const getTrackNumber: (track: TrackEntry) => import("./segments/all-segments").UintWithSize | null;
export declare const getTrackCodec: (track: TrackEntry) => CodecIdSegment | null;
export declare const getTrackTimestampScale: (track: TrackEntry) => import("./segments/all-segments").FloatWithSize | null;
export declare const getTrackByNumber: (tracks: TrackEntry[], id: number) => TrackEntry | undefined;
export declare const getTrackId: (track: TrackEntry) => number;
export declare const getCodecSegment: (track: TrackEntry) => CodecIdSegment | null;
export declare const getColourSegment: (track: TrackEntry) => ColourSegment | null;
export declare const getTransferCharacteristicsSegment: (color: ColourSegment) => TransferCharacteristicsSegment | null;
export declare const getMatrixCoefficientsSegment: (color: ColourSegment) => MatrixCoefficientsSegment | null;
export declare const getPrimariesSegment: (color: ColourSegment) => PrimariesSegment | null;
export declare const getRangeSegment: (color: ColourSegment) => RangeSegment | null;
export declare const getDisplayHeightSegment: (track: TrackEntry) => DisplayHeightSegment | null;
export declare const getTrackTypeSegment: (track: TrackEntry) => TrackTypeSegment | null;
export declare const getWidthSegment: (track: TrackEntry) => WidthSegment | null;
export declare const getHeightSegment: (track: TrackEntry) => HeightSegment | null;
export declare const getDisplayWidthSegment: (track: TrackEntry) => DisplayWidthSegment | null;
export declare const getTracksSegment: (segment: MainSegment) => {
    type: "Tracks";
    value: import("./segments/all-segments").PossibleEbml[];
    minVintWidth: number | null;
} | null;
export declare const getTrackWithUid: (segment: MainSegment, trackUid: string) => number | null;
export declare const getTimescaleSegment: (segment: MainSegment) => TimestampScaleSegment | null;
export declare const getVideoSegment: (track: TrackEntry) => VideoSegment | null;
export declare const getAudioSegment: (track: TrackEntry) => AudioSegment | null;
export declare const getSampleRate: (track: TrackEntry) => number | null;
export declare const getNumberOfChannels: (track: TrackEntry) => number;
export declare const getBitDepth: (track: TrackEntry) => number | null;
export declare const getPrivateData: (track: TrackEntry) => Uint8Array | null;
export declare const getClusterSegment: (segment: MainSegment) => ClusterSegment | null;
