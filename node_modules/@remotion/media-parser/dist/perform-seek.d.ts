import type { MediaParserController } from './controller/media-parser-controller';
import type { PrefetchCache } from './fetch';
import type { AllOptions, ParseMediaFields } from './fields';
import type { BufferIterator } from './iterator/buffer-iterator';
import type { MediaParserLogLevel } from './log';
import type { ParseMediaMode, ParseMediaSrc } from './options';
import type { MediaParserReaderInterface } from './readers/reader';
import type { CurrentReader } from './state/current-reader';
import type { SeekInfiniteLoop } from './state/seek-infinite-loop';
import type { MediaSectionState } from './state/video-section';
export declare const performSeek: ({ seekTo, userInitiated, controller, mediaSection, iterator, seekInfiniteLoop, logLevel, mode, contentLength, currentReader, readerInterface, src, discardReadBytes, fields, prefetchCache, }: {
    seekTo: number;
    userInitiated: boolean;
    controller: MediaParserController;
    mediaSection: MediaSectionState;
    iterator: BufferIterator;
    logLevel: MediaParserLogLevel;
    mode: ParseMediaMode;
    contentLength: number;
    seekInfiniteLoop: SeekInfiniteLoop;
    currentReader: CurrentReader;
    readerInterface: MediaParserReaderInterface;
    fields: Partial<AllOptions<ParseMediaFields>>;
    src: ParseMediaSrc;
    discardReadBytes: (force: boolean) => Promise<void>;
    prefetchCache: PrefetchCache;
}) => Promise<void>;
