import type { MediaParserController } from './controller/media-parser-controller';
import type { PrefetchCache } from './fetch';
import type { AllOptions, ParseMediaFields } from './fields';
import type { <PERSON>ufferIterator } from './iterator/buffer-iterator';
import type { MediaParserLogLevel } from './log';
import type { M3uPlaylistContext, ParseMediaMode, ParseMediaSrc } from './options';
import type { MediaParserReaderInterface } from './readers/reader';
import type { AacState } from './state/aac-state';
import type { AvcState } from './state/avc/avc-state';
import type { CurrentReader } from './state/current-reader';
import type { FlacState } from './state/flac-state';
import type { TracksState } from './state/has-tracks-section';
import type { IsoBaseMediaState } from './state/iso-base-media/iso-state';
import type { KeyframesState } from './state/keyframes';
import type { M3uState } from './state/m3u-state';
import type { WebmState } from './state/matroska/webm';
import type { Mp3State } from './state/mp3';
import type { ParserState } from './state/parser-state';
import type { RiffState } from './state/riff';
import type { SamplesObservedState } from './state/samples-observed/slow-duration-fps';
import type { SeekInfiniteLoop } from './state/seek-infinite-loop';
import type { StructureState } from './state/structure';
import type { TransportStreamState } from './state/transport-stream/transport-stream';
import { type MediaSectionState } from './state/video-section';
export declare const turnSeekIntoByte: ({ seek, mediaSectionState, logLevel, iterator, structureState, m3uPlaylistContext, isoState, transportStream, tracksState, webmState, keyframes, flacState, samplesObserved, riffState, mp3State, contentLength, aacState, m3uState, avcState, }: {
    seek: number;
    mediaSectionState: MediaSectionState;
    logLevel: MediaParserLogLevel;
    iterator: BufferIterator;
    structureState: StructureState;
    m3uPlaylistContext: M3uPlaylistContext | null;
    isoState: IsoBaseMediaState;
    transportStream: TransportStreamState;
    tracksState: TracksState;
    webmState: WebmState;
    keyframes: KeyframesState;
    flacState: FlacState;
    samplesObserved: SamplesObservedState;
    riffState: RiffState;
    mp3State: Mp3State;
    aacState: AacState;
    contentLength: number;
    m3uState: M3uState;
    avcState: AvcState;
}) => Promise<SeekResolution>;
export type WorkOnSeekRequestOptions = {
    logLevel: MediaParserLogLevel;
    controller: MediaParserController;
    isoState: IsoBaseMediaState;
    iterator: BufferIterator;
    structureState: StructureState;
    src: ParseMediaSrc;
    contentLength: number;
    readerInterface: MediaParserReaderInterface;
    mediaSection: MediaSectionState;
    m3uPlaylistContext: M3uPlaylistContext | null;
    transportStream: TransportStreamState;
    mode: ParseMediaMode;
    seekInfiniteLoop: SeekInfiniteLoop;
    currentReader: CurrentReader;
    discardReadBytes: (force: boolean) => Promise<void>;
    fields: Partial<AllOptions<ParseMediaFields>>;
    tracksState: TracksState;
    webmState: WebmState;
    keyframes: KeyframesState;
    flacState: FlacState;
    samplesObserved: SamplesObservedState;
    riffState: RiffState;
    mp3State: Mp3State;
    aacState: AacState;
    m3uState: M3uState;
    prefetchCache: PrefetchCache;
    avcState: AvcState;
};
export declare const getWorkOnSeekRequestOptions: (state: ParserState) => WorkOnSeekRequestOptions;
export declare const workOnSeekRequest: (options: WorkOnSeekRequestOptions) => Promise<void>;
export type SeekResolution = {
    type: 'valid-but-must-wait';
} | {
    type: 'invalid';
} | {
    type: 'intermediary-seek';
    byte: number;
} | {
    type: 'do-seek';
    byte: number;
    timeInSeconds: number;
};
