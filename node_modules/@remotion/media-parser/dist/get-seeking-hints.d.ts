import type { M3uPlaylistContext } from './options';
import type { SeekingHints } from './seeking-hints';
import type { AacState } from './state/aac-state';
import type { FlacState } from './state/flac-state';
import type { TracksState } from './state/has-tracks-section';
import type { IsoBaseMediaState } from './state/iso-base-media/iso-state';
import type { KeyframesState } from './state/keyframes';
import type { WebmState } from './state/matroska/webm';
import type { Mp3State } from './state/mp3';
import type { RiffState } from './state/riff';
import type { SamplesObservedState } from './state/samples-observed/slow-duration-fps';
import type { StructureState } from './state/structure';
import type { TransportStreamState } from './state/transport-stream/transport-stream';
import type { MediaSectionState } from './state/video-section';
export declare const getSeekingHints: ({ structureState, m3uPlaylistContext, mediaSectionState, isoState, transportStream, tracksState, keyframesState, webmState, flacState, samplesObserved, riffState, mp3State, contentLength, aacState, }: {
    structureState: StructureState;
    m3uPlaylistContext: M3uPlaylistContext | null;
    mediaSectionState: MediaSectionState;
    isoState: IsoBaseMediaState;
    transportStream: TransportStreamState;
    tracksState: TracksState;
    keyframesState: KeyframesState;
    webmState: WebmState;
    flacState: FlacState;
    samplesObserved: SamplesObservedState;
    riffState: RiffState;
    mp3State: Mp3State;
    aacState: AacState;
    contentLength: number;
}) => SeekingHints | null;
