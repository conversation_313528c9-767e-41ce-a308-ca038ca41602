import type { VideoConfig } from 'remotion/no-react';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './browser-log';
import type { Page } from './browser/BrowserPage';
import type { SourceMapGetter } from './browser/source-map-getter';
import type { VideoImageFormat } from './image-format';
import type { LogLevel } from './log-level';
import type { BrowserReplacer } from './replace-browser';
export declare const makePage: ({ context, initialFrame, browserReplacer, logLevel, indent, pagesArray, onBrowserLog, scale, timeoutInMilliseconds, composition, proxyPort, serveUrl, muted, envVariables, serializedInputPropsWithCustomSchema, imageFormat, serializedResolvedPropsWithCustomSchema, pageIndex, }: {
    context: SourceMapGetter;
    initialFrame: number;
    browserReplacer: BrowserReplacer;
    logLevel: LogLevel;
    indent: boolean;
    pagesArray: Page[];
    onBrowserLog: ((log: <PERSON><PERSON><PERSON><PERSON>og) => void) | null;
    scale: number;
    timeoutInMilliseconds: number;
    composition: Omit<VideoConfig, "defaultProps" | "props">;
    proxyPort: number;
    serveUrl: string;
    muted: boolean;
    envVariables: Record<string, string>;
    serializedInputPropsWithCustomSchema: string;
    serializedResolvedPropsWithCustomSchema: string;
    imageFormat: VideoImageFormat;
    pageIndex: number;
}) => Promise<Page>;
