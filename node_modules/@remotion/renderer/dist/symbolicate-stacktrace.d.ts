import type { BasicSourceMapConsumer, IndexedSourceMapConsumer } from 'source-map';
import { SourceMapConsumer } from 'source-map';
import type { UnsymbolicatedStackFrame } from './parse-browser-error-stack';
export declare const getSourceMapFromRemoteUrl: (url: string) => Promise<IndexedSourceMapConsumer>;
type ScriptLine = {
    lineNumber: number;
    content: string;
    highlight: boolean;
};
export type SymbolicatedStackFrame = {
    originalFunctionName: string | null;
    originalFileName: string | null;
    originalLineNumber: number | null;
    originalColumnNumber: number | null;
    originalScriptCode: ScriptLine[] | null;
};
export declare const symbolicateStackTraceFromRemoteFrames: (frames: UnsymbolicatedStackFrame[]) => Promise<SymbolicatedStackFrame[]>;
export declare const symbolicateFromSources: (frames: UnsymbolicatedStackFrame[], mapValues: Record<string, SourceMapConsumer | null>) => SymbolicatedStackFrame[];
export declare const symbolicateStackFrame: (frame: UnsymbolicatedStackFrame, map: SourceMapConsumer) => {
    originalColumnNumber: number | null;
    originalFileName: string | null;
    originalFunctionName: string | null;
    originalLineNumber: number | null;
    originalScriptCode: ScriptLine[] | null;
};
export declare const getSourceMapFromRemoteFile: (fileName: string) => Promise<AnySourceMapConsumer | null>;
export declare const getSourceMapFromLocalFile: (fileName: string) => Promise<AnySourceMapConsumer | null>;
export type AnySourceMapConsumer = BasicSourceMapConsumer | IndexedSourceMapConsumer;
export {};
