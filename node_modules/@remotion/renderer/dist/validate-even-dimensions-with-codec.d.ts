import type { Codec } from './codec';
import type { LogLevel } from './log-level';
export declare const validateEvenDimensionsWithCodec: ({ width, height, codec, scale, wantsImageSequence, indent, logLevel, }: {
    width: number;
    height: number;
    scale: number;
    codec: Codec;
    wantsImageSequence: boolean;
    indent: boolean;
    logLevel: LogLevel;
}) => {
    actualWidth: number;
    actualHeight: number;
};
