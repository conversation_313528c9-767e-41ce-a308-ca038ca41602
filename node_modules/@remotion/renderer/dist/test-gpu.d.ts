import type { BrowserExecutable } from './browser-executable';
import type { LogLevel } from './log-level';
import type { ChromiumOptions } from './open-browser';
import type { ChromeMode } from './options/chrome-mode';
import type { OnBrowserDownload } from './options/on-browser-download';
type Item = {
    feature: string;
    status: string;
};
export declare const getChromiumGpuInformation: ({ browserExecutable, indent, logLevel, chromiumOptions, timeoutInMilliseconds, onBrowserDownload, chromeMode, }: {
    browserExecutable: BrowserExecutable;
    indent: boolean;
    logLevel: LogLevel;
    chromiumOptions: ChromiumOptions;
    timeoutInMilliseconds: number;
    onBrowserDownload: OnBrowserDownload;
    chromeMode: ChromeMode;
}) => Promise<Item[]>;
export {};
