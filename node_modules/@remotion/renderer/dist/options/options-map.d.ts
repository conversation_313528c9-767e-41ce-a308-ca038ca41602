export declare const optionsMap: {
    readonly renderMedia: {
        readonly offthreadVideoCacheSizeInBytes: {
            name: string;
            cliFlag: "offthreadvideo-cache-size-in-bytes";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoCacheSizeInBytes";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly offthreadVideoThreads: {
            name: string;
            cliFlag: "offthreadvideo-video-threads";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoThreads";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly videoBitrate: {
            name: string;
            cliFlag: "video-bitrate";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string | null;
            };
            setConfig: (bitrate: string | null) => void;
        };
        readonly numberOfGifLoops: {
            name: string;
            cliFlag: "number-of-gif-loops";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "numberOfGifLoops";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: number;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (newLoop: import("./number-of-gif-loops").NumberOfGifLoops) => void;
        };
        readonly repro: {
            name: string;
            cliFlag: "repro";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: boolean;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: boolean;
                source: string;
            };
            setConfig: (should: boolean) => void;
        };
        readonly x264Preset: {
            name: string;
            cliFlag: "x264-preset";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "x264Preset";
            docLink: string;
            type: import("./x264-preset").X264Preset | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./x264-preset").X264Preset;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (profile: import("./x264-preset").X264Preset | null) => void;
        };
        readonly audioBitrate: {
            name: string;
            cliFlag: "audio-bitrate";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: string;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (value: string | null) => void;
        };
        readonly colorSpace: {
            name: string;
            cliFlag: "color-space";
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            ssrName: string;
            type: import("./color-space").ColorSpace | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: import("./color-space").ColorSpace;
            };
            setConfig: (value: "default" | "bt709" | "bt2020-ncl" | null) => void;
        };
        readonly codec: {
            name: string;
            cliFlag: "codec";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: import("..").Codec;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }, { compositionCodec, configFile, downloadName, outName, uiCodec, }: {
                outName: string | null;
                downloadName: string | null;
                configFile: import("..").Codec | null;
                uiCodec: import("..").Codec | null;
                compositionCodec: import("..").Codec | null;
            }) => {
                value: import("..").Codec;
                source: string;
            };
            setConfig: (newCodec: import("..").CodecOrUndefined) => void;
        };
        readonly disallowParallelEncoding: {
            name: string;
            cliFlag: "disallow-parallel-encoding";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: boolean;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: boolean;
                source: string;
            };
            setConfig(value: boolean): void;
        };
        readonly jpegQuality: {
            name: string;
            cliFlag: "jpeg-quality";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: number;
            setConfig: (q: number | undefined) => void;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
        };
        readonly encodingMaxRate: {
            name: string;
            cliFlag: "max-rate";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "encodingMaxRate";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: string;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (newMaxRate: string | null) => void;
        };
        readonly encodingBufferSize: {
            name: string;
            cliFlag: "buffer-size";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "encodingBufferSize";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: string;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (bitrate: string | null) => void;
        };
        readonly muted: {
            name: string;
            cliFlag: "muted";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: boolean;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: boolean;
            };
            setConfig: () => void;
        };
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("..").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("..").LogLevel) => void;
            type: import("..").LogLevel;
        };
        readonly timeoutInMilliseconds: {
            name: string;
            cliFlag: "timeout";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "timeoutInMilliseconds";
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (value: number) => void;
        };
        readonly binariesDirectory: {
            name: string;
            cliFlag: "binaries-directory";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "binariesDirectory";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string | null;
            };
            setConfig: (value: string | null) => void;
        };
        readonly forSeamlessAacConcatenation: {
            name: string;
            cliFlag: "for-seamless-aac-concatenation";
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: true;
            } | {
                source: string;
                value: false;
            };
            setConfig: (value: boolean) => void;
            ssrName: string;
            type: boolean;
        };
        readonly separateAudioTo: {
            cliFlag: string;
            description: () => string;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string;
            } | {
                source: string;
                value: null;
            };
            name: string;
            setConfig: () => never;
            ssrName: string;
            type: string | null;
        };
        readonly audioCodec: {
            cliFlag: "audio-codec";
            setConfig: (audioCodec: "mp3" | "aac" | "pcm-16" | "opus" | null) => void;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: import("./audio-codec").AudioCodec;
            } | {
                source: string;
                value: null;
            };
            description: () => string;
            docLink: string;
            name: string;
            ssrName: "audioCodec";
            type: import("./audio-codec").AudioCodec;
        };
        readonly onBrowserDownload: {
            name: string;
            cliFlag: "on-browser-download";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "onBrowserDownload";
            docLink: string;
            type: import("./on-browser-download").OnBrowserDownload;
            getValue: () => never;
            setConfig: () => never;
        };
        readonly hardwareAcceleration: {
            name: string;
            cliFlag: "hardware-acceleration";
            description: () => string;
            ssrName: string;
            docLink: string;
            type: import("./hardware-acceleration").HardwareAccelerationOption;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: "disable" | "if-possible" | "required";
            };
            setConfig: (value: import("./hardware-acceleration").HardwareAccelerationOption) => void;
        };
        readonly chromeMode: {
            cliFlag: "chrome-mode";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./chrome-mode").ChromeMode;
                source: string;
            };
            setConfig: (newChromeMode: import("./chrome-mode").ChromeMode) => void;
            type: import("./chrome-mode").ChromeMode;
        };
    };
    readonly stitchFramesToVideo: {
        readonly separateAudioTo: {
            cliFlag: string;
            description: () => string;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string;
            } | {
                source: string;
                value: null;
            };
            name: string;
            setConfig: () => never;
            ssrName: string;
            type: string | null;
        };
        readonly hardwareAcceleration: {
            name: string;
            cliFlag: "hardware-acceleration";
            description: () => string;
            ssrName: string;
            docLink: string;
            type: import("./hardware-acceleration").HardwareAccelerationOption;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: "disable" | "if-possible" | "required";
            };
            setConfig: (value: import("./hardware-acceleration").HardwareAccelerationOption) => void;
        };
    };
    readonly renderStill: {
        readonly offthreadVideoCacheSizeInBytes: {
            name: string;
            cliFlag: "offthreadvideo-cache-size-in-bytes";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoCacheSizeInBytes";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly offthreadVideoThreads: {
            name: string;
            cliFlag: "offthreadvideo-video-threads";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoThreads";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly jpegQuality: {
            name: string;
            cliFlag: "jpeg-quality";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: number;
            setConfig: (q: number | undefined) => void;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
        };
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("..").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("..").LogLevel) => void;
            type: import("..").LogLevel;
        };
        readonly timeoutInMilliseconds: {
            name: string;
            cliFlag: "timeout";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "timeoutInMilliseconds";
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (value: number) => void;
        };
        readonly binariesDirectory: {
            name: string;
            cliFlag: "binaries-directory";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "binariesDirectory";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string | null;
            };
            setConfig: (value: string | null) => void;
        };
        readonly onBrowserDownload: {
            name: string;
            cliFlag: "on-browser-download";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "onBrowserDownload";
            docLink: string;
            type: import("./on-browser-download").OnBrowserDownload;
            getValue: () => never;
            setConfig: () => never;
        };
        readonly chromeMode: {
            cliFlag: "chrome-mode";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./chrome-mode").ChromeMode;
                source: string;
            };
            setConfig: (newChromeMode: import("./chrome-mode").ChromeMode) => void;
            type: import("./chrome-mode").ChromeMode;
        };
    };
    readonly getCompositions: {
        readonly offthreadVideoCacheSizeInBytes: {
            name: string;
            cliFlag: "offthreadvideo-cache-size-in-bytes";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoCacheSizeInBytes";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly offthreadVideoThreads: {
            name: string;
            cliFlag: "offthreadvideo-video-threads";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoThreads";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("..").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("..").LogLevel) => void;
            type: import("..").LogLevel;
        };
        readonly timeoutInMilliseconds: {
            name: string;
            cliFlag: "timeout";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "timeoutInMilliseconds";
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (value: number) => void;
        };
        readonly binariesDirectory: {
            name: string;
            cliFlag: "binaries-directory";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "binariesDirectory";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string | null;
            };
            setConfig: (value: string | null) => void;
        };
        readonly onBrowserDownload: {
            name: string;
            cliFlag: "on-browser-download";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "onBrowserDownload";
            docLink: string;
            type: import("./on-browser-download").OnBrowserDownload;
            getValue: () => never;
            setConfig: () => never;
        };
        readonly chromeMode: {
            cliFlag: "chrome-mode";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./chrome-mode").ChromeMode;
                source: string;
            };
            setConfig: (newChromeMode: import("./chrome-mode").ChromeMode) => void;
            type: import("./chrome-mode").ChromeMode;
        };
    };
    readonly selectComposition: {
        readonly offthreadVideoCacheSizeInBytes: {
            name: string;
            cliFlag: "offthreadvideo-cache-size-in-bytes";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoCacheSizeInBytes";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly offthreadVideoThreads: {
            name: string;
            cliFlag: "offthreadvideo-video-threads";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoThreads";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("..").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("..").LogLevel) => void;
            type: import("..").LogLevel;
        };
        readonly timeoutInMilliseconds: {
            name: string;
            cliFlag: "timeout";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "timeoutInMilliseconds";
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (value: number) => void;
        };
        readonly binariesDirectory: {
            name: string;
            cliFlag: "binaries-directory";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "binariesDirectory";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string | null;
            };
            setConfig: (value: string | null) => void;
        };
        readonly onBrowserDownload: {
            name: string;
            cliFlag: "on-browser-download";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "onBrowserDownload";
            docLink: string;
            type: import("./on-browser-download").OnBrowserDownload;
            getValue: () => never;
            setConfig: () => never;
        };
        readonly chromeMode: {
            cliFlag: "chrome-mode";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./chrome-mode").ChromeMode;
                source: string;
            };
            setConfig: (newChromeMode: import("./chrome-mode").ChromeMode) => void;
            type: import("./chrome-mode").ChromeMode;
        };
    };
    readonly renderFrames: {
        readonly forSeamlessAacConcatenation: {
            name: string;
            cliFlag: "for-seamless-aac-concatenation";
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: true;
            } | {
                source: string;
                value: false;
            };
            setConfig: (value: boolean) => void;
            ssrName: string;
            type: boolean;
        };
        readonly offthreadVideoCacheSizeInBytes: {
            name: string;
            cliFlag: "offthreadvideo-cache-size-in-bytes";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoCacheSizeInBytes";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly offthreadVideoThreads: {
            name: string;
            cliFlag: "offthreadvideo-video-threads";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoThreads";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly jpegQuality: {
            name: string;
            cliFlag: "jpeg-quality";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: number;
            setConfig: (q: number | undefined) => void;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
        };
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("..").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("..").LogLevel) => void;
            type: import("..").LogLevel;
        };
        readonly timeoutInMilliseconds: {
            name: string;
            cliFlag: "timeout";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "timeoutInMilliseconds";
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (value: number) => void;
        };
        readonly binariesDirectory: {
            name: string;
            cliFlag: "binaries-directory";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "binariesDirectory";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string | null;
            };
            setConfig: (value: string | null) => void;
        };
        readonly onBrowserDownload: {
            name: string;
            cliFlag: "on-browser-download";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "onBrowserDownload";
            docLink: string;
            type: import("./on-browser-download").OnBrowserDownload;
            getValue: () => never;
            setConfig: () => never;
        };
        readonly chromeMode: {
            cliFlag: "chrome-mode";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./chrome-mode").ChromeMode;
                source: string;
            };
            setConfig: (newChromeMode: import("./chrome-mode").ChromeMode) => void;
            type: import("./chrome-mode").ChromeMode;
        };
        readonly imageSequencePattern: {
            name: string;
            cliFlag: "image-sequence-pattern";
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: null;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: string;
                source: string;
            };
            setConfig: (pattern: string | null) => void;
        };
    };
    readonly renderMediaOnLambda: {
        readonly offthreadVideoCacheSizeInBytes: {
            name: string;
            cliFlag: "offthreadvideo-cache-size-in-bytes";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoCacheSizeInBytes";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly offthreadVideoThreads: {
            name: string;
            cliFlag: "offthreadvideo-video-threads";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoThreads";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly videoBitrate: {
            name: string;
            cliFlag: "video-bitrate";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string | null;
            };
            setConfig: (bitrate: string | null) => void;
        };
        readonly numberOfGifLoops: {
            name: string;
            cliFlag: "number-of-gif-loops";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "numberOfGifLoops";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: number;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (newLoop: import("./number-of-gif-loops").NumberOfGifLoops) => void;
        };
        readonly preferLossless: {
            name: string;
            cliFlag: "prefer-lossless";
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            type: boolean;
            ssrName: "preferLossless";
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: true;
                source: string;
            } | {
                value: false;
                source: string;
            };
            setConfig: (val: boolean) => void;
        };
        readonly audioBitrate: {
            name: string;
            cliFlag: "audio-bitrate";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: string;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (value: string | null) => void;
        };
        readonly deleteAfter: {
            name: string;
            cliFlag: "delete-after";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "deleteAfter";
            docLink: string;
            type: import("./delete-after").DeleteAfter | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: import("./delete-after").DeleteAfter;
            } | {
                source: string;
                value: null;
            };
            setConfig: (value: import("./delete-after").DeleteAfter | null) => void;
        };
        readonly x264Preset: {
            name: string;
            cliFlag: "x264-preset";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "x264Preset";
            docLink: string;
            type: import("./x264-preset").X264Preset | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./x264-preset").X264Preset;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (profile: import("./x264-preset").X264Preset | null) => void;
        };
        readonly encodingMaxRate: {
            name: string;
            cliFlag: "max-rate";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "encodingMaxRate";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: string;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (newMaxRate: string | null) => void;
        };
        readonly encodingBufferSize: {
            name: string;
            cliFlag: "buffer-size";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "encodingBufferSize";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: string;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (bitrate: string | null) => void;
        };
        readonly colorSpace: {
            name: string;
            cliFlag: "color-space";
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            ssrName: string;
            type: import("./color-space").ColorSpace | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: import("./color-space").ColorSpace;
            };
            setConfig: (value: "default" | "bt709" | "bt2020-ncl" | null) => void;
        };
        readonly muted: {
            name: string;
            cliFlag: "muted";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: boolean;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: boolean;
            };
            setConfig: () => void;
        };
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("..").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("..").LogLevel) => void;
            type: import("..").LogLevel;
        };
        readonly timeoutInMilliseconds: {
            name: string;
            cliFlag: "timeout";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "timeoutInMilliseconds";
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (value: number) => void;
        };
        readonly apiKey: {
            name: string;
            cliFlag: "api-key";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "apiKey";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string | null;
            };
            setConfig: (value: string | null) => void;
        };
    };
    readonly renderStillOnLambda: {
        readonly offthreadVideoCacheSizeInBytes: {
            name: string;
            cliFlag: "offthreadvideo-cache-size-in-bytes";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoCacheSizeInBytes";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly offthreadVideoThreads: {
            name: string;
            cliFlag: "offthreadvideo-video-threads";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoThreads";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly jpegQuality: {
            name: string;
            cliFlag: "jpeg-quality";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: number;
            setConfig: (q: number | undefined) => void;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
        };
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("..").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("..").LogLevel) => void;
            type: import("..").LogLevel;
        };
        readonly deleteAfter: {
            name: string;
            cliFlag: "delete-after";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "deleteAfter";
            docLink: string;
            type: import("./delete-after").DeleteAfter | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: import("./delete-after").DeleteAfter;
            } | {
                source: string;
                value: null;
            };
            setConfig: (value: import("./delete-after").DeleteAfter | null) => void;
        };
        readonly scale: {
            name: string;
            cliFlag: "scale";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (scale: number) => void;
        };
        readonly timeoutInMilliseconds: {
            name: string;
            cliFlag: "timeout";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "timeoutInMilliseconds";
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (value: number) => void;
        };
        readonly apiKey: {
            name: string;
            cliFlag: "api-key";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "apiKey";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string | null;
            };
            setConfig: (value: string | null) => void;
        };
    };
    readonly getCompositionsOnLambda: {
        readonly offthreadVideoCacheSizeInBytes: {
            name: string;
            cliFlag: "offthreadvideo-cache-size-in-bytes";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoCacheSizeInBytes";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly offthreadVideoThreads: {
            name: string;
            cliFlag: "offthreadvideo-video-threads";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoThreads";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("..").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("..").LogLevel) => void;
            type: import("..").LogLevel;
        };
        readonly timeoutInMilliseconds: {
            name: string;
            cliFlag: "timeout";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "timeoutInMilliseconds";
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (value: number) => void;
        };
    };
    readonly renderMediaOnCloudRun: {
        readonly offthreadVideoCacheSizeInBytes: {
            name: string;
            cliFlag: "offthreadvideo-cache-size-in-bytes";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoCacheSizeInBytes";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly offthreadVideoThreads: {
            name: string;
            cliFlag: "offthreadvideo-video-threads";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoThreads";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly numberOfGifLoops: {
            name: string;
            cliFlag: "number-of-gif-loops";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "numberOfGifLoops";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: number;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (newLoop: import("./number-of-gif-loops").NumberOfGifLoops) => void;
        };
        readonly preferLossless: {
            name: string;
            cliFlag: "prefer-lossless";
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            type: boolean;
            ssrName: "preferLossless";
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: true;
                source: string;
            } | {
                value: false;
                source: string;
            };
            setConfig: (val: boolean) => void;
        };
        readonly colorSpace: {
            name: string;
            cliFlag: "color-space";
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            ssrName: string;
            type: import("./color-space").ColorSpace | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: import("./color-space").ColorSpace;
            };
            setConfig: (value: "default" | "bt709" | "bt2020-ncl" | null) => void;
        };
        readonly audioBitrate: {
            name: string;
            cliFlag: "audio-bitrate";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: string;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (value: string | null) => void;
        };
        readonly videoBitrate: {
            name: string;
            cliFlag: "video-bitrate";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string | null;
            };
            setConfig: (bitrate: string | null) => void;
        };
        readonly x264Preset: {
            name: string;
            cliFlag: "x264-preset";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "x264Preset";
            docLink: string;
            type: import("./x264-preset").X264Preset | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./x264-preset").X264Preset;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (profile: import("./x264-preset").X264Preset | null) => void;
        };
        readonly encodingMaxRate: {
            name: string;
            cliFlag: "max-rate";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "encodingMaxRate";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: string;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (newMaxRate: string | null) => void;
        };
        readonly encodingBufferSize: {
            name: string;
            cliFlag: "buffer-size";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "encodingBufferSize";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: string;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (bitrate: string | null) => void;
        };
        readonly muted: {
            name: string;
            cliFlag: "muted";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: boolean;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: boolean;
            };
            setConfig: () => void;
        };
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("..").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("..").LogLevel) => void;
            type: import("..").LogLevel;
        };
        readonly delayRenderTimeoutInMilliseconds: {
            name: string;
            cliFlag: "timeout";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "timeoutInMilliseconds";
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (value: number) => void;
        };
        readonly enforceAudioTrack: {
            name: string;
            cliFlag: "enforce-audio-track";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: boolean;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: true;
            } | {
                source: string;
                value: false;
            };
            setConfig: (value: boolean) => void;
        };
        readonly scale: {
            name: string;
            cliFlag: "scale";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (scale: number) => void;
        };
        readonly crf: {
            name: string;
            cliFlag: "crf";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: import("..").Crf;
            };
            setConfig: (crf: import("..").Crf) => void;
        };
        readonly jpegQuality: {
            name: string;
            cliFlag: "jpeg-quality";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: number;
            setConfig: (q: number | undefined) => void;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
        };
    };
    readonly renderStillOnCloudRun: {
        readonly offthreadVideoCacheSizeInBytes: {
            name: string;
            cliFlag: "offthreadvideo-cache-size-in-bytes";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoCacheSizeInBytes";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly offthreadVideoThreads: {
            name: string;
            cliFlag: "offthreadvideo-video-threads";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoThreads";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("..").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("..").LogLevel) => void;
            type: import("..").LogLevel;
        };
        readonly scale: {
            name: string;
            cliFlag: "scale";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (scale: number) => void;
        };
        readonly jpegQuality: {
            name: string;
            cliFlag: "jpeg-quality";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: number;
            setConfig: (q: number | undefined) => void;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
        };
        readonly delayRenderTimeoutInMilliseconds: {
            name: string;
            cliFlag: "timeout";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "timeoutInMilliseconds";
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (value: number) => void;
        };
    };
    readonly ensureBrowser: {
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("..").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("..").LogLevel) => void;
            type: import("..").LogLevel;
        };
        readonly onBrowserDownload: {
            name: string;
            cliFlag: "on-browser-download";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "onBrowserDownload";
            docLink: string;
            type: import("./on-browser-download").OnBrowserDownload;
            getValue: () => never;
            setConfig: () => never;
        };
        readonly chromeMode: {
            cliFlag: "chrome-mode";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./chrome-mode").ChromeMode;
                source: string;
            };
            setConfig: (newChromeMode: import("./chrome-mode").ChromeMode) => void;
            type: import("./chrome-mode").ChromeMode;
        };
    };
    readonly openBrowser: {
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("..").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("..").LogLevel) => void;
            type: import("..").LogLevel;
        };
        readonly onBrowserDownload: {
            name: string;
            cliFlag: "on-browser-download";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "onBrowserDownload";
            docLink: string;
            type: import("./on-browser-download").OnBrowserDownload;
            getValue: () => never;
            setConfig: () => never;
        };
        readonly chromeMode: {
            cliFlag: "chrome-mode";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./chrome-mode").ChromeMode;
                source: string;
            };
            setConfig: (newChromeMode: import("./chrome-mode").ChromeMode) => void;
            type: import("./chrome-mode").ChromeMode;
        };
    };
    readonly deploySiteLambda: {
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("..").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("..").LogLevel) => void;
            type: import("..").LogLevel;
        };
        readonly throwIfSiteExists: {
            cliFlag: string;
            description: () => string;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: boolean;
            };
            name: string;
            setConfig: () => never;
            ssrName: string;
            type: boolean;
        };
    };
    readonly deploySiteCloudRun: {
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("..").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("..").LogLevel) => void;
            type: import("..").LogLevel;
        };
    };
};
