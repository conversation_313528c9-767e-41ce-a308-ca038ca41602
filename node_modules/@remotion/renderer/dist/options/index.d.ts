import type { AnyRemotionOption } from './option';
export declare const allOptions: {
    audioCodecOption: {
        cliFlag: "audio-codec";
        setConfig: (audioCodec: "mp3" | "aac" | "pcm-16" | "opus" | null) => void;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: import("./audio-codec").AudioCodec;
        } | {
            source: string;
            value: null;
        };
        description: () => string;
        docLink: string;
        name: string;
        ssrName: "audioCodec";
        type: import("./audio-codec").AudioCodec;
    };
    scaleOption: {
        name: string;
        cliFlag: "scale";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: string;
        docLink: string;
        type: number;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: number;
        };
        setConfig: (scale: number) => void;
    };
    crfOption: {
        name: string;
        cliFlag: "crf";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: string;
        docLink: string;
        type: number;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: import("..").Crf;
        };
        setConfig: (crf: import("..").Crf) => void;
    };
    jpegQualityOption: {
        name: string;
        cliFlag: "jpeg-quality";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: string;
        docLink: string;
        type: number;
        setConfig: (q: number | undefined) => void;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: number;
        };
    };
    videoBitrateOption: {
        name: string;
        cliFlag: "video-bitrate";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: string;
        docLink: string;
        type: string | null;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: string | null;
        };
        setConfig: (bitrate: string | null) => void;
    };
    audioBitrateOption: {
        name: string;
        cliFlag: "audio-bitrate";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: string;
        docLink: string;
        type: string;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            value: string;
            source: string;
        } | {
            value: null;
            source: string;
        };
        setConfig: (value: string | null) => void;
    };
    enforceAudioOption: {
        name: string;
        cliFlag: "enforce-audio-track";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: string;
        docLink: string;
        type: boolean;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: true;
        } | {
            source: string;
            value: false;
        };
        setConfig: (value: boolean) => void;
    };
    mutedOption: {
        name: string;
        cliFlag: "muted";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: string;
        docLink: string;
        type: boolean;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: boolean;
        };
        setConfig: () => void;
    };
    videoCodecOption: {
        name: string;
        cliFlag: "codec";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: string;
        docLink: string;
        type: import("..").Codec;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }, { compositionCodec, configFile, downloadName, outName, uiCodec, }: {
            outName: string | null;
            downloadName: string | null;
            configFile: import("..").Codec | null;
            uiCodec: import("..").Codec | null;
            compositionCodec: import("..").Codec | null;
        }) => {
            value: import("..").Codec;
            source: string;
        };
        setConfig: (newCodec: import("..").CodecOrUndefined) => void;
    };
    offthreadVideoCacheSizeInBytesOption: {
        name: string;
        cliFlag: "offthreadvideo-cache-size-in-bytes";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: "offthreadVideoCacheSizeInBytes";
        docLink: string;
        type: number | null;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: number;
        } | {
            source: string;
            value: null;
        };
        setConfig: (size: number | null) => void;
    };
    offthreadVideoThreadsOption: {
        name: string;
        cliFlag: "offthreadvideo-video-threads";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: "offthreadVideoThreads";
        docLink: string;
        type: number | null;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: number;
        } | {
            source: string;
            value: null;
        };
        setConfig: (size: number | null) => void;
    };
    webhookCustomDataOption: {
        name: string;
        cliFlag: "webhook-custom-data";
        description: (type: "cli" | "ssr") => import("react/jsx-runtime").JSX.Element;
        ssrName: "customData";
        docLink: string;
        type: Record<string, unknown> | null;
        getValue: () => never;
        setConfig: () => never;
    };
    colorSpaceOption: {
        name: string;
        cliFlag: "color-space";
        description: () => import("react/jsx-runtime").JSX.Element;
        docLink: string;
        ssrName: string;
        type: import("./color-space").ColorSpace | null;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: import("./color-space").ColorSpace;
        };
        setConfig: (value: "default" | "bt709" | "bt2020-ncl" | null) => void;
    };
    deleteAfterOption: {
        name: string;
        cliFlag: "delete-after";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: "deleteAfter";
        docLink: string;
        type: import("./delete-after").DeleteAfter | null;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: import("./delete-after").DeleteAfter;
        } | {
            source: string;
            value: null;
        };
        setConfig: (value: import("./delete-after").DeleteAfter | null) => void;
    };
    disallowParallelEncodingOption: {
        name: string;
        cliFlag: "disallow-parallel-encoding";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: string;
        docLink: string;
        type: boolean;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            value: boolean;
            source: string;
        };
        setConfig(value: boolean): void;
    };
    folderExpiryOption: {
        name: string;
        cliFlag: "enable-folder-expiry";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: "enableFolderExpiry";
        docLink: string;
        type: boolean | null;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: boolean | null;
        };
        setConfig: (value: boolean | null) => void;
    };
    enableMultiprocessOnLinuxOption: {
        name: string;
        cliFlag: "enable-multiprocess-on-linux";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: string;
        docLink: string;
        type: boolean;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: boolean;
        };
        setConfig: (value: boolean) => void;
    };
    glOption: {
        cliFlag: "gl";
        docLink: string;
        name: string;
        type: import("./gl").OpenGlRenderer | null;
        ssrName: string;
        description: () => import("react/jsx-runtime").JSX.Element;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            value: import("./gl").OpenGlRenderer;
            source: string;
        } | {
            value: null;
            source: string;
        };
        setConfig: (value: import("./gl").OpenGlRenderer | null) => void;
    };
    enableLambdaInsights: {
        name: string;
        cliFlag: "enable-lambda-insights";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: string;
        docLink: string;
        type: boolean;
        setConfig: (value: boolean) => void;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            value: boolean;
            source: string;
        };
    };
    encodingMaxRateOption: {
        name: string;
        cliFlag: "max-rate";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: "encodingMaxRate";
        docLink: string;
        type: string | null;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            value: string;
            source: string;
        } | {
            value: null;
            source: string;
        };
        setConfig: (newMaxRate: string | null) => void;
    };
    encodingBufferSizeOption: {
        name: string;
        cliFlag: "buffer-size";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: "encodingBufferSize";
        docLink: string;
        type: string | null;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            value: string;
            source: string;
        } | {
            value: null;
            source: string;
        };
        setConfig: (bitrate: string | null) => void;
    };
    beepOnFinishOption: {
        name: string;
        cliFlag: "beep-on-finish";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: null;
        docLink: string;
        type: boolean;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            value: boolean;
            source: string;
        };
        setConfig(value: boolean): void;
    };
    numberOfGifLoopsOption: {
        name: string;
        cliFlag: "number-of-gif-loops";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: "numberOfGifLoops";
        docLink: string;
        type: number | null;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            value: number;
            source: string;
        } | {
            value: null;
            source: string;
        };
        setConfig: (newLoop: import("./number-of-gif-loops").NumberOfGifLoops) => void;
    };
    reproOption: {
        name: string;
        cliFlag: "repro";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: string;
        docLink: string;
        type: boolean;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            value: boolean;
            source: string;
        };
        setConfig: (should: boolean) => void;
    };
    preferLosslessOption: {
        name: string;
        cliFlag: "prefer-lossless";
        description: () => import("react/jsx-runtime").JSX.Element;
        docLink: string;
        type: boolean;
        ssrName: "preferLossless";
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            value: true;
            source: string;
        } | {
            value: false;
            source: string;
        };
        setConfig: (val: boolean) => void;
    };
    x264Option: {
        name: string;
        cliFlag: "x264-preset";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: "x264Preset";
        docLink: string;
        type: import("./x264-preset").X264Preset | null;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            value: import("./x264-preset").X264Preset;
            source: string;
        } | {
            value: null;
            source: string;
        };
        setConfig: (profile: import("./x264-preset").X264Preset | null) => void;
    };
    logLevelOption: {
        cliFlag: "log";
        name: string;
        ssrName: string;
        description: () => import("react/jsx-runtime").JSX.Element;
        docLink: string;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            value: import("..").LogLevel;
            source: string;
        };
        setConfig: (newLogLevel: import("..").LogLevel) => void;
        type: import("..").LogLevel;
    };
    delayRenderTimeoutInMillisecondsOption: {
        name: string;
        cliFlag: "timeout";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: "timeoutInMilliseconds";
        docLink: string;
        type: number;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: number;
        };
        setConfig: (value: number) => void;
    };
    headlessOption: {
        name: string;
        cliFlag: "disable-headless";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: string;
        docLink: string;
        type: boolean;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: boolean;
        };
        setConfig: (value: boolean) => void;
    };
    overwriteOption: {
        name: string;
        cliFlag: "overwrite";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: string;
        docLink: string;
        type: boolean;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }, defaultValue: boolean) => {
            source: string;
            value: boolean;
        };
        setConfig: (value: boolean) => void;
    };
    binariesDirectoryOption: {
        name: string;
        cliFlag: "binaries-directory";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: "binariesDirectory";
        docLink: string;
        type: string | null;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: string | null;
        };
        setConfig: (value: string | null) => void;
    };
    forSeamlessAacConcatenationOption: {
        name: string;
        cliFlag: "for-seamless-aac-concatenation";
        description: () => import("react/jsx-runtime").JSX.Element;
        docLink: string;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: true;
        } | {
            source: string;
            value: false;
        };
        setConfig: (value: boolean) => void;
        ssrName: string;
        type: boolean;
    };
    separateAudioOption: {
        cliFlag: string;
        description: () => string;
        docLink: string;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: string;
        } | {
            source: string;
            value: null;
        };
        name: string;
        setConfig: () => never;
        ssrName: string;
        type: string | null;
    };
    publicPathOption: {
        name: string;
        cliFlag: "public-path";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: "publicPath";
        docLink: string;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: string;
        } | {
            source: string;
            value: null;
        };
        setConfig: (value: string | null) => void;
        type: string | null;
    };
    publicDirOption: {
        name: string;
        cliFlag: "public-dir";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: "publicDir";
        docLink: string;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: string;
        } | {
            source: string;
            value: null;
        };
        setConfig: (value: string | null) => void;
        type: string | null;
    };
    onBrowserDownloadOption: {
        name: string;
        cliFlag: "on-browser-download";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: "onBrowserDownload";
        docLink: string;
        type: import("./on-browser-download").OnBrowserDownload;
        getValue: () => never;
        setConfig: () => never;
    };
    throwIfSiteExistsOption: {
        cliFlag: string;
        description: () => string;
        docLink: string;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: boolean;
        };
        name: string;
        setConfig: () => never;
        ssrName: string;
        type: boolean;
    };
    disableGitSourceOption: {
        cliFlag: string;
        description: () => string;
        docLink: string;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: boolean;
        };
        name: string;
        setConfig: () => never;
        ssrName: string;
        type: boolean;
    };
    metadataOption: {
        name: string;
        cliFlag: "metadata";
        description: (mode: "cli" | "ssr") => import("react/jsx-runtime").JSX.Element;
        docLink: string;
        type: import("./metadata").Metadata;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: {
                [k: string]: string;
            };
        };
        setConfig: (newMetadata: import("./metadata").Metadata) => void;
        ssrName: string;
    };
    hardwareAccelerationOption: {
        name: string;
        cliFlag: "hardware-acceleration";
        description: () => string;
        ssrName: string;
        docLink: string;
        type: import("./hardware-acceleration").HardwareAccelerationOption;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: "disable" | "if-possible" | "required";
        };
        setConfig: (value: import("./hardware-acceleration").HardwareAccelerationOption) => void;
    };
    chromeModeOption: {
        cliFlag: "chrome-mode";
        name: string;
        ssrName: string;
        description: () => import("react/jsx-runtime").JSX.Element;
        docLink: string;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            value: import("./chrome-mode").ChromeMode;
            source: string;
        };
        setConfig: (newChromeMode: import("./chrome-mode").ChromeMode) => void;
        type: import("./chrome-mode").ChromeMode;
    };
    apiKeyOption: {
        name: string;
        cliFlag: "api-key";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: "apiKey";
        docLink: string;
        type: string | null;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            source: string;
            value: string | null;
        };
        setConfig: (value: string | null) => void;
    };
    audioLatencyHintOption: {
        name: string;
        cliFlag: "audio-latency-hint";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: "audioLatencyHint";
        docLink: string;
        type: AudioContextLatencyCategory;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            value: AudioContextLatencyCategory;
            source: string;
        } | {
            value: null;
            source: string;
        };
        setConfig: (profile: AudioContextLatencyCategory | null) => void;
    };
    enableCrossSiteIsolationOption: {
        name: string;
        cliFlag: "cross-site-isolation";
        description: () => import("react/jsx-runtime").JSX.Element;
        ssrName: null;
        docLink: string;
        type: boolean;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            value: boolean;
            source: string;
        };
        setConfig(value: boolean): void;
    };
    imageSequencePatternOption: {
        name: string;
        cliFlag: "image-sequence-pattern";
        ssrName: string;
        description: () => import("react/jsx-runtime").JSX.Element;
        docLink: null;
        type: string | null;
        getValue: ({ commandLine }: {
            commandLine: Record<string, unknown>;
        }) => {
            value: string;
            source: string;
        };
        setConfig: (pattern: string | null) => void;
    };
};
export type AvailableOptions = keyof typeof allOptions;
export type TypeOfOption<Type> = Type extends AnyRemotionOption<infer X> ? X : never;
