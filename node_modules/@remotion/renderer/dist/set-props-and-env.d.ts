import type { Page } from './browser/BrowserPage';
import type { LogLevel } from './log-level';
type SetPropsAndEnv = {
    serializedInputPropsWithCustomSchema: string;
    envVariables: Record<string, string> | undefined;
    page: Page;
    serveUrl: string;
    initialFrame: number;
    timeoutInMilliseconds: number | undefined;
    proxyPort: number;
    retriesRemaining: number;
    audioEnabled: boolean;
    videoEnabled: boolean;
    indent: boolean;
    logLevel: LogLevel;
    onServeUrlVisited: () => void;
};
export declare const setPropsAndEnv: (params: SetPropsAndEnv) => Promise<unknown>;
export {};
