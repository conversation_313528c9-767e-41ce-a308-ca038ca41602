import type { RenderMediaOnDownload } from './assets/download-and-map-assets-to-file';
import type { DownloadMap } from './assets/download-map';
import type { Page } from './browser/BrowserPage';
import type { CountType } from './get-frame-padded-index';
import type { VideoImageFormat } from './image-format';
import type { LogLevel } from './log-level';
import type { CancelSignal } from './make-cancel-signal';
import type { FrameAndAssets, OnArtifact } from './render-frames';
export declare const renderFrameWithOptionToReject: ({ reject, width, height, compId, attempt, stoppedSignal, indent, logLevel, timeoutInMilliseconds, outputDir, onFrameBuffer, imageFormat, onError, lastFrame, jpegQuality, frameDir, scale, countType, assets, framesToRender, onArtifact, onDownload, downloadMap, binariesDirectory, cancelSignal, framesRenderedObj, onFrameUpdate, frame, page, imageSequencePattern, }: {
    reject: (err: Error) => void;
    width: number;
    height: number;
    compId: string;
    attempt: number;
    stoppedSignal: {
        stopped: boolean;
    };
    timeoutInMilliseconds: number;
    indent: boolean;
    logLevel: LogLevel;
    outputDir: string | null;
    onFrameBuffer: null | ((buffer: Buffer, frame: number) => void) | undefined;
    imageFormat: VideoImageFormat;
    onError: (err: Error) => void;
    lastFrame: number;
    jpegQuality: number;
    frameDir: string;
    scale: number;
    countType: CountType;
    assets: FrameAndAssets[];
    framesToRender: number[];
    onArtifact: OnArtifact | null;
    onDownload: RenderMediaOnDownload | null;
    downloadMap: DownloadMap;
    binariesDirectory: string | null;
    cancelSignal: CancelSignal | undefined;
    framesRenderedObj: {
        count: number;
    };
    onFrameUpdate: null | ((framesRendered: number, frameIndex: number, timeToRenderInMilliseconds: number) => void);
    frame: number;
    page: Page;
    imageSequencePattern: string | null;
}) => Promise<undefined>;
