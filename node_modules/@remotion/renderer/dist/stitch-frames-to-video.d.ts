import type { RenderMediaOnDownload } from './assets/download-and-map-assets-to-file';
import type { RenderAssetInfo } from './assets/download-map';
import type { Codec } from './codec';
import type { FfmpegOverrideFn } from './ffmpeg-override';
import type { LogLevel } from './log-level';
import type { CancelSignal } from './make-cancel-signal';
import type { AudioCodec } from './options/audio-codec';
import { type ColorSpace } from './options/color-space';
import type { ToOptions } from './options/option';
import type { optionsMap } from './options/options-map';
import type { X264Preset } from './options/x264-preset';
import type { PixelFormat } from './pixel-format';
import type { ProResProfile } from './prores-profile';
type InternalStitchFramesToVideoOptions = {
    audioBitrate: string | null;
    videoBitrate: string | null;
    maxRate: string | null;
    bufferSize: string | null;
    fps: number;
    width: number;
    height: number;
    outputLocation: string | null;
    force: boolean;
    assetsInfo: RenderAssetInfo;
    pixelFormat: PixelFormat;
    numberOfGifLoops: number | null;
    codec: Codec;
    audioCodec: AudioCodec | null;
    crf: number | null;
    onProgress?: null | ((progress: number) => void);
    onDownload: undefined | RenderMediaOnDownload;
    proResProfile: undefined | ProResProfile;
    logLevel: LogLevel;
    cancelSignal: CancelSignal | null;
    preEncodedFileLocation: string | null;
    preferLossless: boolean;
    indent: boolean;
    muted: boolean;
    x264Preset: X264Preset | null;
    enforceAudioTrack: boolean;
    ffmpegOverride: null | FfmpegOverrideFn;
    colorSpace: ColorSpace | null;
    binariesDirectory: string | null;
    metadata: Record<string, string> | null;
} & ToOptions<typeof optionsMap.stitchFramesToVideo>;
export type StitchFramesToVideoOptions = {
    fps: number;
    width: number;
    height: number;
    assetsInfo: RenderAssetInfo;
    force?: boolean;
    audioBitrate?: string | null;
    videoBitrate?: string | null;
    maxRate?: string | null;
    bufferSize?: string | null;
    outputLocation?: string | null;
    pixelFormat?: PixelFormat;
    numberOfGifLoops?: number | null;
    codec?: Codec;
    audioCodec?: AudioCodec | null;
    crf?: number | null;
    onProgress?: (progress: number) => void;
    onDownload?: RenderMediaOnDownload;
    proResProfile?: ProResProfile;
    verbose?: boolean;
    cancelSignal?: CancelSignal;
    muted?: boolean;
    enforceAudioTrack?: boolean;
    ffmpegOverride?: FfmpegOverrideFn;
    x264Preset?: X264Preset | null;
    colorSpace?: ColorSpace;
    binariesDirectory?: string | null;
    metadata?: Record<string, string> | null;
} & Partial<ToOptions<typeof optionsMap.stitchFramesToVideo>>;
export declare const internalStitchFramesToVideo: (options: InternalStitchFramesToVideoOptions) => Promise<Buffer | null>;
export declare const stitchFramesToVideo: ({ assetsInfo, force, fps, height, width, audioBitrate, audioCodec, cancelSignal, codec, crf, enforceAudioTrack, ffmpegOverride, muted, numberOfGifLoops, onDownload, onProgress, outputLocation, pixelFormat, proResProfile, verbose, videoBitrate, maxRate, bufferSize, x264Preset, colorSpace, binariesDirectory, separateAudioTo, metadata, hardwareAcceleration, }: StitchFramesToVideoOptions) => Promise<Buffer | null>;
export {};
