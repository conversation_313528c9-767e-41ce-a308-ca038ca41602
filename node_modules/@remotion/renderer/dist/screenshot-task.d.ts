import type { Page } from './browser/BrowserPage';
import type { StillImageFormat } from './image-format';
export declare const screenshotTask: ({ format, height, omitBackground, page, width, path, jpegQuality, scale, }: {
    page: Page;
    format: StillImageFormat;
    path?: string;
    jpegQuality?: number;
    omitBackground: boolean;
    width: number;
    height: number;
    scale: number;
}) => Promise<Buffer>;
