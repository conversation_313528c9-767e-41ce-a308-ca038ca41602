{"repository": {"url": "https://github.com/remotion-dev/remotion/tree/main/packages/renderer"}, "name": "@remotion/renderer", "version": "4.0.322", "description": "Render Remotion videos using Node.js or Bun", "main": "dist/index.js", "types": "dist/index.d.ts", "sideEffects": false, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "SEE LICENSE IN LICENSE.md", "bugs": {"url": "https://github.com/remotion-dev/remotion/issues"}, "dependencies": {"execa": "5.1.1", "extract-zip": "2.0.1", "source-map": "^0.8.0-beta.0", "ws": "8.17.1", "@remotion/streaming": "4.0.322", "remotion": "4.0.322"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"@testing-library/dom": "10.4.0", "@testing-library/react": "16.1.0", "@happy-dom/global-registrator": "14.5.1", "react": "19.0.0", "react-dom": "19.0.0", "@types/ws": "8.5.10", "eslint": "9.19.0", "@remotion/example-videos": "4.0.322", "@remotion/eslint-config-internal": "4.0.322"}, "optionalDependencies": {"@remotion/compositor-darwin-arm64": "4.0.322", "@remotion/compositor-darwin-x64": "4.0.322", "@remotion/compositor-linux-arm64-musl": "4.0.322", "@remotion/compositor-linux-x64-gnu": "4.0.322", "@remotion/compositor-linux-x64-musl": "4.0.322", "@remotion/compositor-win32-x64-msvc": "4.0.322", "@remotion/compositor-linux-arm64-gnu": "4.0.322"}, "keywords": ["remotion", "ffmpeg", "video", "react", "player"], "publishConfig": {"access": "public"}, "homepage": "https://www.remotion.dev/docs/renderer", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "module": "./dist/esm/index.mjs", "import": "./dist/esm/index.mjs"}, "./client": {"types": "./dist/client.d.ts", "require": "./dist/client.js", "module": "./dist/esm/client.mjs", "import": "./dist/esm/client.mjs"}, "./pure": {"types": "./dist/pure.d.ts", "require": "./dist/pure.js", "module": "./dist/esm/pure.mjs", "import": "./dist/esm/pure.mjs"}, "./error-handling": {"types": "./dist/error-handling.d.ts", "require": "./dist/error-handling.js", "module": "./dist/esm/error-handling.mjs", "import": "./dist/esm/error-handling.mjs"}}, "typesVersions": {">=1.0": {"client": ["./dist/client.d.ts"], "pure": ["./dist/pure.d.ts"], "error-handling": ["./dist/error-handling.d.ts"]}}, "scripts": {"formatting": "prettier --experimental-cli src --check", "lint": "eslint src", "test": "bun test src", "make": "tsc -d && bun --env-file=../.env.bundle bundle.ts"}}