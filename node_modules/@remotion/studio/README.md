# @remotion/studio
 
APIs for interacting with the Remotion Studio
 
[![NPM Downloads](https://img.shields.io/npm/dm/@remotion/studio.svg?style=flat&color=black&label=Downloads)](https://npmcharts.com/compare/@remotion/studio?minimal=true)
 
## Installation
 
```bash
npm install @remotion/studio --save-exact
```
 
When installing a Remotion package, make sure to align the version of all `remotion` and `@remotion/*` packages to the same version.
Remove the `^` character from the version number to use the exact version.
 
## Usage
 
See the [documentation](https://www.remotion.dev/docs/studio/api) for more information.
