import type { _InternalTypes, StaticFile, TFolder } from 'remotion';
import type { CompositionSelectorItemType } from '../components/CompositionSelectorItem';
export type AssetFolder = {
    name: string;
    items: AssetStructure;
    expanded: boolean;
};
export type AssetStructure = {
    files: StaticFile[];
    folders: AssetFolder[];
};
export declare const buildAssetFolderStructure: (files: StaticFile[], parentFolderName: string | null, foldersExpanded: Record<string, boolean>) => AssetStructure;
export declare const splitParentIntoNameAndParent: (name: string | null) => {
    name: string | null;
    parent: string | null;
};
export declare const createFolderTree: (comps: _InternalTypes["AnyComposition"][], folders: TFolder[], foldersExpanded: Record<string, boolean>) => CompositionSelectorItemType[];
