import type { Codec } from '@remotion/renderer';
import type { RenderType } from '../components/RenderModal/RenderModalAdvanced';
type Section = 'general' | 'picture' | 'advanced' | 'data' | 'gif' | 'audio';
export declare const useRenderModalSections: (renderMode: RenderType, codec: Codec) => {
    tab: Section;
    setTab: import("react").Dispatch<import("react").SetStateAction<Section>>;
    shownTabs: Section[];
};
export {};
