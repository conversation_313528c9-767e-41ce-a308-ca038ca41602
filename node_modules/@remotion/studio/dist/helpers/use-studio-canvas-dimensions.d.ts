import type { Size } from '@remotion/player';
import type { AssetMetadata } from './get-asset-metadata';
import type { Dimensions } from './is-current-selected-still';
export declare const useStudioCanvasDimensions: ({ canvasSize, contentDimensions, assetMetadata, }: {
    canvasSize: Size | null;
    contentDimensions: Dimensions | "none" | null;
    assetMetadata: AssetMetadata | null;
}) => {
    canvasPosition: {
        left: number;
        top: number;
        width: number;
        height: number;
    };
    scale: number;
};
