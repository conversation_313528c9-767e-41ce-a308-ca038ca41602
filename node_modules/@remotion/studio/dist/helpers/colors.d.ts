export declare const BACKGROUND = "rgb(31,36,40)";
export declare const BACKGROUND__TRANSPARENT = "rgba(31,36,40, 0)";
export declare const INPUT_BACKGROUND = "#2f363d";
export declare const BORDER_COLOR = "#000";
export declare const LIGHT_COLOR = "#ddd";
export declare const SELECTED_BACKGROUND = "hsla(0, 0%, 100%, 0.15)";
export declare const LIGHT_TEXT = "#A6A7A9";
export declare const RULER_COLOR = "#808080";
export declare const VERY_LIGHT_TEXT = "rgba(255, 255, 255, 0.3)";
export declare const CLEAR_HOVER = "rgba(255, 255, 255, 0.06)";
export declare const INPUT_BORDER_COLOR_UNHOVERED = "rgba(0, 0, 0, 0.6)";
export declare const INPUT_BORDER_COLOR_HOVERED = "rgba(255, 255, 255, 0.05)";
export declare const TIMELINE_BACKGROUND = "#111";
export declare const FAIL_COLOR = "#ff3232";
export declare const TEXT_COLOR = "#fff";
export declare const WARNING_COLOR = "#f1c40f";
export declare const BLUE = "#0b84f3";
export declare const BLUE_DISABLED = "#284f73";
export declare const LIGHT_TRANSPARENT = "rgba(255, 255, 255, 0.7)";
export declare const UNSELECTED_GUIDE = "#7e1219";
export declare const SELECTED_GUIDE = "#d22d3a";
export declare const LINE_COLOR = "#363A3E";
export declare const TIMELINE_TRACK_SEPARATOR = "rgba(0, 0, 0, 0.3)";
export declare const getBackgroundFromHoverState: ({ selected, hovered, }: {
    selected: boolean;
    hovered: boolean;
}) => "hsla(0, 0%, 100%, 0.15)" | "hsla(0, 0%, 100%, 0.25)" | "rgba(255, 255, 255, 0.06)" | "transparent";
