import type { EventSourceEvent } from '@remotion/studio-shared';
import React from 'react';
type PreviewServerState = {
    type: 'init';
} | {
    type: 'connected';
    clientId: string;
} | {
    type: 'disconnected';
};
type Context = {
    previewServerState: PreviewServerState;
    subscribeToEvent: (type: EventSourceEvent['type'], listener: (event: EventSourceEvent) => void) => () => void;
};
export declare const StudioServerConnectionCtx: React.Context<Context>;
export declare const PreviewServerConnection: React.FC<{
    readonly children: React.ReactNode;
    readonly readOnlyStudio: boolean;
}>;
export {};
