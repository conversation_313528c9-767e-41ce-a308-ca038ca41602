import type { Size } from '@remotion/player';
import type { Guide } from '../state/editor-guides';
export declare const drawMarkingOnRulerCanvas: ({ scale, points, startMarking, originOffset, markingGaps, orientation, rulerCanvasRef, selectedGuide, canvasHeight, canvasWidth, }: {
    scale: number;
    points: Array<{
        position: number;
        value: number;
    }>;
    startMarking: number;
    originOffset: number;
    markingGaps: number;
    orientation: "horizontal" | "vertical";
    rulerCanvasRef: React.RefObject<HTMLCanvasElement | null>;
    selectedGuide: Guide | null;
    canvasWidth: number;
    canvasHeight: number;
}) => void;
export declare const getRulerPoints: ({ rulerScaleRange, rulerMarkingGaps, scale, }: {
    rulerScaleRange: {
        start: number;
        end: number;
    };
    rulerMarkingGaps: number;
    scale: number;
}) => {
    points: {
        value: number;
        position: number;
    }[];
    startMarking: number;
};
export declare const getRulerScaleRange: ({ canvasLength, scale, canvasSize, }: {
    canvasLength: number;
    scale: number;
    canvasSize: Size;
}) => {
    start: number;
    end: number;
};
