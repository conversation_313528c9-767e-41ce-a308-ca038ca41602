import React from 'react';
export type ZodType = Awaited<typeof import('zod')>['z'];
export type ZodTypesType = Awaited<typeof import('@remotion/zod-types')>;
export declare const getZodIfPossible: () => Promise<ZodType | null>;
export declare const getZTypesIfPossible: () => Promise<ZodTypesType | null>;
export declare const useZodIfPossible: () => typeof import("zod/lib/external") | null;
export declare const useZodTypesIfPossible: () => typeof import("@remotion/zod-types") | null;
export declare const ZodProvider: React.FC<{
    readonly children: React.ReactNode;
}>;
