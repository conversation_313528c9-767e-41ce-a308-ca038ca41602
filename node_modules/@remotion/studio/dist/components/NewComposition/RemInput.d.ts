import type { PropsWithChildren } from 'react';
import React from 'react';
export type RemInputStatus = 'error' | 'warning' | 'ok';
type Props = React.DetailedHTMLProps<React.InputHTMLAttributes<HTMLInputElement>, HTMLInputElement> & {
    readonly status: RemInputStatus;
    readonly rightAlign: boolean;
};
export declare const INPUT_HORIZONTAL_PADDING = 8;
export declare const RightAlignInput: React.FC<PropsWithChildren>;
export declare const inputBaseStyle: React.CSSProperties;
export declare const getInputBorderColor: ({ status, isFocused, isHovered, }: {
    status: "error" | "warning" | "ok";
    isFocused: boolean;
    isHovered: boolean;
}) => "hsla(0, 0%, 100%, 0.15)" | "rgba(0, 0, 0, 0.6)" | "rgba(255, 255, 255, 0.05)" | "#ff3232" | "#f1c40f";
export declare const RemotionInput: React.ForwardRefExoticComponent<Omit<Props, "ref"> & React.RefAttributes<HTMLInputElement>>;
export {};
