import type { InputHTMLAttributes } from 'react';
import React from 'react';
import type { RemInputStatus } from './RemInput';
export declare const InputDragger: React.ForwardRefExoticComponent<InputHTMLAttributes<HTMLInputElement> & {
    readonly onValueChange: (newVal: number) => void;
    readonly onTextChange: (newVal: string) => void;
    readonly status: RemInputStatus;
    readonly formatter?: (str: number | string) => string;
    readonly rightAlign: boolean;
} & React.RefAttributes<HTMLButtonElement>>;
