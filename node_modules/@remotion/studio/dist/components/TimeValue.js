"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimeValue = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
const player_1 = require("@remotion/player");
const react_1 = require("react");
const remotion_1 = require("remotion");
const colors_1 = require("../helpers/colors");
const is_current_selected_still_1 = require("../helpers/is-current-selected-still");
const use_keybinding_1 = require("../helpers/use-keybinding");
const render_frame_1 = require("../state/render-frame");
const InputDragger_1 = require("./NewComposition/InputDragger");
const layout_1 = require("./layout");
const text = {
    color: 'white',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    fontVariantNumeric: 'tabular-nums',
    lineHeight: 1,
    width: '100%',
    userSelect: 'none',
    WebkitUserSelect: 'none',
};
const time = {
    display: 'inline-block',
    fontSize: 16,
    lineHeight: 1,
    fontFamily: 'monospace',
};
const frameStyle = {
    color: colors_1.LIGHT_TEXT,
    fontWeight: 500,
    lineHeight: 1,
    fontSize: 16,
    fontFamily: 'monospace',
    paddingRight: 10,
};
const TimeValue = () => {
    const frame = (0, remotion_1.useCurrentFrame)();
    const config = remotion_1.Internals.useUnsafeVideoConfig();
    const isStill = (0, is_current_selected_still_1.useIsStill)();
    const { seek, play, pause, toggle } = player_1.PlayerInternals.usePlayer();
    const keybindings = (0, use_keybinding_1.useKeybinding)();
    const ref = (0, react_1.useRef)(null);
    const onTextChange = (0, react_1.useCallback)((newVal) => {
        seek(parseInt(newVal, 10));
    }, [seek]);
    const onValueChange = (0, react_1.useCallback)((val) => {
        seek(val);
    }, [seek]);
    (0, react_1.useImperativeHandle)(remotion_1.Internals.timeValueRef, () => ({
        goToFrame: () => {
            var _a;
            (_a = ref.current) === null || _a === void 0 ? void 0 : _a.click();
        },
        seek,
        play,
        pause,
        toggle,
    }), [seek, play, pause, toggle]);
    (0, react_1.useEffect)(() => {
        const gKey = keybindings.registerKeybinding({
            event: 'keypress',
            key: 'g',
            callback: () => {
                var _a;
                (_a = ref.current) === null || _a === void 0 ? void 0 : _a.click();
            },
            commandCtrlKey: false,
            preventDefault: true,
            triggerIfInputFieldFocused: false,
            keepRegisteredWhenNotHighestContext: false,
        });
        return () => {
            gKey.unregister();
        };
    }, [keybindings]);
    if (!config) {
        return null;
    }
    if (isStill) {
        return null;
    }
    return ((0, jsx_runtime_1.jsxs)("div", { style: text, children: [(0, jsx_runtime_1.jsx)("div", { style: time, children: (0, render_frame_1.renderFrame)(frame, config.fps) }), (0, jsx_runtime_1.jsx)(layout_1.Spacing, { x: 2 }), (0, jsx_runtime_1.jsx)(layout_1.Flex, {}), (0, jsx_runtime_1.jsx)(InputDragger_1.InputDragger, { ref: ref, value: frame, onTextChange: onTextChange, onValueChange: onValueChange, rightAlign: true, status: "ok", style: frameStyle })] }));
};
exports.TimeValue = TimeValue;
