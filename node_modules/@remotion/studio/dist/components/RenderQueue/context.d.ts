import type { RenderJob } from '@remotion/studio-shared';
import React from 'react';
declare global {
    interface Window {
        remotion_initialRenderQueue: RenderJob[] | null;
    }
}
type RenderQueueContextType = {
    jobs: RenderJob[];
};
export declare const RenderQueueContext: React.Context<RenderQueueContextType>;
export declare const renderJobsRef: React.RefObject<{
    updateRenderJobs: (jobs: RenderJob[]) => void;
} | null>;
export declare const RenderQueueContextProvider: React.FC<{
    readonly children: React.ReactNode;
}>;
export {};
