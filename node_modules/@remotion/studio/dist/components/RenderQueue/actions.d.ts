import type { AudioCodec, ChromeMode, Codec, ColorSpace, LogLevel, PixelFormat, ProResProfile, StillImageFormat, VideoImageFormat, X264Preset } from '@remotion/renderer';
import type { HardwareAccelerationOption } from '@remotion/renderer/client';
import type { CanUpdateDefaultPropsResponse, EnumPath, RecastCodemod, RenderJob, RequiredChromiumOptions, VisualControlChange } from '@remotion/studio-shared';
export declare const addStillRenderJob: ({ compositionId, outName, imageFormat, jpegQuality, frame, scale, logLevel, chromiumOptions, delayRenderTimeout, envVariables, inputProps, offthreadVideoCacheSizeInBytes, offthreadVideoThreads, multiProcessOnLinux, beepOnFinish, metadata, chromeMode, }: {
    compositionId: string;
    outName: string;
    imageFormat: StillImageFormat;
    jpegQuality: number;
    frame: number;
    scale: number;
    logLevel: LogLevel;
    chromiumOptions: RequiredChromiumOptions;
    delayRenderTimeout: number;
    envVariables: Record<string, string>;
    inputProps: Record<string, unknown>;
    offthreadVideoCacheSizeInBytes: number | null;
    offthreadVideoThreads: number | null;
    multiProcessOnLinux: boolean;
    beepOnFinish: boolean;
    metadata: Record<string, string> | null;
    chromeMode: ChromeMode;
}) => Promise<undefined>;
export declare const addSequenceRenderJob: ({ compositionId, outName, imageFormat, startFrame, endFrame, scale, logLevel, chromiumOptions, delayRenderTimeout, envVariables, inputProps, concurrency, offthreadVideoCacheSizeInBytes, offthreadVideoThreads, jpegQuality, disallowParallelEncoding, multiProcessOnLinux, beepOnFinish, repro, metadata, chromeMode, }: {
    compositionId: string;
    outName: string;
    imageFormat: VideoImageFormat;
    jpegQuality: number;
    startFrame: number;
    endFrame: number;
    scale: number;
    logLevel: LogLevel;
    chromiumOptions: RequiredChromiumOptions;
    concurrency: number;
    delayRenderTimeout: number;
    envVariables: Record<string, string>;
    inputProps: Record<string, unknown>;
    offthreadVideoCacheSizeInBytes: number | null;
    offthreadVideoThreads: number | null;
    disallowParallelEncoding: boolean;
    multiProcessOnLinux: boolean;
    beepOnFinish: boolean;
    repro: boolean;
    metadata: Record<string, string> | null;
    chromeMode: ChromeMode;
}) => Promise<undefined>;
export declare const addVideoRenderJob: ({ compositionId, outName, imageFormat, jpegQuality, scale, logLevel, codec, concurrency, crf, startFrame, endFrame, muted, enforceAudioTrack, proResProfile, x264Preset, pixelFormat, audioBitrate, videoBitrate, everyNthFrame, numberOfGifLoops, delayRenderTimeout, audioCodec, disallowParallelEncoding, chromiumOptions, envVariables, inputProps, offthreadVideoCacheSizeInBytes, offthreadVideoThreads, colorSpace, multiProcessOnLinux, encodingMaxRate, encodingBufferSize, beepOnFinish, repro, forSeamlessAacConcatenation, separateAudioTo, metadata, hardwareAcceleration, chromeMode, }: {
    compositionId: string;
    outName: string;
    imageFormat: VideoImageFormat;
    jpegQuality: number | null;
    scale: number;
    logLevel: LogLevel;
    codec: Codec;
    concurrency: number;
    crf: number | null;
    startFrame: number;
    endFrame: number;
    muted: boolean;
    enforceAudioTrack: boolean;
    proResProfile: ProResProfile | null;
    x264Preset: X264Preset | null;
    pixelFormat: PixelFormat;
    audioBitrate: string | null;
    videoBitrate: string | null;
    everyNthFrame: number;
    numberOfGifLoops: number | null;
    delayRenderTimeout: number;
    audioCodec: AudioCodec;
    disallowParallelEncoding: boolean;
    chromiumOptions: RequiredChromiumOptions;
    envVariables: Record<string, string>;
    inputProps: Record<string, unknown>;
    offthreadVideoCacheSizeInBytes: number | null;
    offthreadVideoThreads: number | null;
    colorSpace: ColorSpace;
    multiProcessOnLinux: boolean;
    encodingMaxRate: string | null;
    encodingBufferSize: string | null;
    beepOnFinish: boolean;
    repro: boolean;
    forSeamlessAacConcatenation: boolean;
    separateAudioTo: string | null;
    metadata: Record<string, string> | null;
    hardwareAcceleration: HardwareAccelerationOption;
    chromeMode: ChromeMode;
}) => Promise<undefined>;
export declare const unsubscribeFromFileExistenceWatcher: ({ file, clientId, }: {
    file: string;
    clientId: string;
}) => Promise<undefined>;
export declare const subscribeToFileExistenceWatcher: ({ file, clientId, }: {
    file: string;
    clientId: string;
}) => Promise<boolean>;
export declare const openInFileExplorer: ({ directory }: {
    directory: string;
}) => Promise<void>;
export declare const applyCodemod: ({ codemod, dryRun, signal, }: {
    codemod: RecastCodemod;
    dryRun: boolean;
    signal: AbortController["signal"];
}) => Promise<import("@remotion/studio-shared").ApplyCodemodResponse>;
export declare const removeRenderJob: (job: RenderJob) => Promise<undefined>;
export declare const cancelRenderJob: (job: RenderJob) => Promise<import("@remotion/studio-shared").CancelRenderResponse>;
export declare const updateAvailable: (signal: AbortSignal) => Promise<import("@remotion/studio-shared").UpdateAvailableResponse>;
export declare const getProjectInfo: (signal: AbortSignal) => Promise<import("@remotion/studio-shared").ProjectInfoResponse>;
export declare const callUpdateDefaultPropsApi: (compositionId: string, defaultProps: Record<string, unknown>, enumPaths: EnumPath[]) => Promise<import("@remotion/studio-shared").UpdateDefaultPropsResponse>;
export declare const canUpdateDefaultProps: (compositionId: string, readOnlyStudio: boolean) => Promise<CanUpdateDefaultPropsResponse>;
export declare const applyVisualControlChange: ({ fileName, changes, }: {
    fileName: string;
    changes: VisualControlChange[];
}) => Promise<import("@remotion/studio-shared").ApplyVisualControlResponse>;
