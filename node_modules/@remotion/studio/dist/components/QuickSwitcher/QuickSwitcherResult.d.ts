import React from 'react';
type QuickSwitcherResultDetail = {
    type: 'composition';
    compositionType: 'composition' | 'still';
} | {
    type: 'menu-item';
} | {
    type: 'search-result';
    titleLine: string;
    subtitleLine: string;
};
export type TQuickSwitcherResult = {
    title: string;
    id: string;
    onSelected: () => void;
} & QuickSwitcherResultDetail;
export declare const QuickSwitcherResult: React.FC<{
    readonly result: TQuickSwitcherResult;
    readonly selected: boolean;
}>;
export {};
