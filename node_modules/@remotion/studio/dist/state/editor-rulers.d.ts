type State = {
    editorShowRulers: boolean;
    setEditorShowRulers: (cb: (prevState: boolean) => boolean) => void;
};
export declare const persistEditorShowRulersOption: (option: boolean) => void;
export declare const loadEditorShowRulersOption: () => boolean;
export declare const EditorShowRulersContext: import("react").Context<State>;
export declare const RULER_WIDTH = 20;
export declare const MINIMUM_VISIBLE_CANVAS_SIZE = 50;
export declare const PREDEFINED_RULER_SCALE_GAPS: number[];
export declare const MAXIMUM_PREDEFINED_RULER_SCALE_GAP = 5000;
export declare const MINIMUM_RULER_MARKING_GAP_PX = 50;
export {};
