export type Guide = {
    orientation: 'horizontal' | 'vertical';
    position: number;
    show: boolean;
    id: string;
    compositionId: string;
};
export type GuideState = {
    editorShowGuides: boolean;
    setEditorShowGuides: (cb: (prevState: boolean) => boolean) => void;
    guidesList: Guide[];
    setGuidesList: (cb: (prevState: Guide[]) => Guide[]) => void;
    selectedGuideId: string | null;
    setSelectedGuideId: (cb: (prevState: string | null) => string | null) => void;
    setHoveredGuideId: (cb: (prevState: string | null) => string | null) => void;
    hoveredGuideId: string | null;
    shouldCreateGuideRef: React.MutableRefObject<boolean>;
    shouldDeleteGuideRef: React.MutableRefObject<boolean>;
};
export declare const persistEditorShowGuidesOption: (option: boolean) => void;
export declare const loadEditorShowGuidesOption: () => boolean;
export declare const persistGuidesList: (guides: Guide[]) => void;
export declare const loadGuidesList: () => Guide[];
export declare const EditorShowGuidesContext: import("react").Context<GuideState>;
