import React from 'react';
import type { ExpandedFoldersState } from '../helpers/persist-open-folders';
type TFolderContext = {
    compositionFoldersExpanded: ExpandedFoldersState;
    setCompositionFoldersExpanded: React.Dispatch<React.SetStateAction<ExpandedFoldersState>>;
    assetFoldersExpanded: ExpandedFoldersState;
    setAssetFoldersExpanded: React.Dispatch<React.SetStateAction<ExpandedFoldersState>>;
};
export declare const FolderContext: React.Context<TFolderContext>;
export declare const FolderContextProvider: React.FC<{
    readonly children: React.ReactNode;
}>;
export {};
