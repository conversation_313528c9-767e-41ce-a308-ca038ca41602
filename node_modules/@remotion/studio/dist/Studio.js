"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Studio = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
const react_1 = require("react");
const react_dom_1 = require("react-dom");
const remotion_1 = require("remotion");
const Editor_1 = require("./components/Editor");
const EditorContexts_1 = require("./components/EditorContexts");
const ServerDisconnected_1 = require("./components/Notifications/ServerDisconnected");
const inject_css_1 = require("./helpers/inject-css");
const getServerDisconnectedDomElement = () => {
    return document.getElementById('server-disconnected-overlay');
};
const Studio = ({ rootComponent, readOnly }) => {
    var _a;
    (0, react_1.useLayoutEffect)(() => {
        window.remotion_isStudio = true;
        window.remotion_isReadOnlyStudio = readOnly;
        remotion_1.Internals.enableSequenceStackTraces();
        return () => {
            window.remotion_isStudio = false;
            window.remotion_isReadOnlyStudio = false;
        };
    }, [readOnly]);
    (0, react_1.useLayoutEffect)(() => {
        (0, inject_css_1.injectCSS)();
    }, []);
    return ((0, jsx_runtime_1.jsx)(remotion_1.Internals.RemotionRoot, { logLevel: window.remotion_logLevel, numberOfAudioTags: window.remotion_numberOfAudioTags, onlyRenderComposition: null, currentCompositionMetadata: null, audioLatencyHint: (_a = window.remotion_audioLatencyHint) !== null && _a !== void 0 ? _a : 'interactive', children: (0, jsx_runtime_1.jsxs)(EditorContexts_1.EditorContexts, { readOnlyStudio: readOnly, children: [(0, jsx_runtime_1.jsx)(Editor_1.Editor, { readOnlyStudio: readOnly, Root: rootComponent }), readOnly
                    ? null
                    : (0, react_dom_1.createPortal)((0, jsx_runtime_1.jsx)(ServerDisconnected_1.ServerDisconnected, {}), getServerDisconnectedDomElement())] }) }));
};
exports.Studio = Studio;
